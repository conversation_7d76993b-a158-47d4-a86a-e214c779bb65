using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Data.Repositories
{
    /// <summary>
    /// واجهة مستودع الرعية
    /// </summary>
    public interface IRaayahRepository
    {
        /// <summary>
        /// الحصول على جميع الرعية
        /// </summary>
        /// <param name="includeInactive">تضمين غير النشطين</param>
        /// <returns>قائمة الرعية</returns>
        Task<IEnumerable<Raayah>> GetAllAsync(bool includeInactive = false);

        /// <summary>
        /// الحصول على رعوي بالمعرف
        /// </summary>
        /// <param name="id">معرف الرعوي</param>
        /// <returns>بيانات الرعوي أو null</returns>
        Task<Raayah?> GetByIdAsync(int id);

        /// <summary>
        /// الحصول على رعوي بالاسم
        /// </summary>
        /// <param name="fullName">الاسم الكامل</param>
        /// <returns>بيانات الرعوي أو null</returns>
        Task<Raayah?> GetByNameAsync(string fullName);

        /// <summary>
        /// البحث في الرعية بالاسم
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="includeInactive">تضمين غير النشطين</param>
        /// <returns>قائمة الرعية المطابقة</returns>
        Task<IEnumerable<Raayah>> SearchByNameAsync(string searchTerm, bool includeInactive = false);

        /// <summary>
        /// الحصول على الرعية النشطين فقط
        /// </summary>
        /// <returns>قائمة الرعية النشطين</returns>
        Task<IEnumerable<Raayah>> GetActiveAsync();

        /// <summary>
        /// الحصول على الرعية في كشف الأوزري
        /// </summary>
        /// <returns>قائمة الرعية في كشف الأوزري</returns>
        Task<IEnumerable<Raayah>> GetKashfOzriAsync();

        /// <summary>
        /// الحصول على الرعية في خارج الكشف
        /// </summary>
        /// <returns>قائمة الرعية في خارج الكشف</returns>
        Task<IEnumerable<Raayah>> GetKharijKashfAsync();

        /// <summary>
        /// الحصول على الرعية الذين لديهم خصم حوالة مفعل
        /// </summary>
        /// <returns>قائمة الرعية مع خصم الحوالة</returns>
        Task<IEnumerable<Raayah>> GetWithDiscountEnabledAsync();

        /// <summary>
        /// إضافة رعوي جديد
        /// </summary>
        /// <param name="raayah">بيانات الرعوي</param>
        /// <returns>الرعوي المضاف</returns>
        Task<Raayah> AddAsync(Raayah raayah);

        /// <summary>
        /// تحديث بيانات رعوي
        /// </summary>
        /// <param name="raayah">بيانات الرعوي المحدثة</param>
        /// <returns>الرعوي المحدث</returns>
        Task<Raayah> UpdateAsync(Raayah raayah);

        /// <summary>
        /// حذف رعوي (حذف ناعم - تعطيل)
        /// </summary>
        /// <param name="id">معرف الرعوي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        Task<bool> SoftDeleteAsync(int id);

        /// <summary>
        /// حذف رعوي نهائياً
        /// </summary>
        /// <param name="id">معرف الرعوي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// استعادة رعوي محذوف (تفعيل)
        /// </summary>
        /// <param name="id">معرف الرعوي</param>
        /// <returns>true إذا تمت الاستعادة بنجاح</returns>
        Task<bool> RestoreAsync(int id);

        /// <summary>
        /// التحقق من وجود رعوي بنفس الاسم
        /// </summary>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="excludeId">معرف الرعوي المستثنى من التحقق</param>
        /// <returns>true إذا كان الاسم موجود</returns>
        Task<bool> ExistsAsync(string fullName, int? excludeId = null);

        /// <summary>
        /// الحصول على عدد الرعية
        /// </summary>
        /// <param name="includeInactive">تضمين غير النشطين</param>
        /// <returns>عدد الرعية</returns>
        Task<int> GetCountAsync(bool includeInactive = false);

        /// <summary>
        /// الحصول على الرعية مع ديونهم الأسبوعية
        /// </summary>
        /// <param name="includeInactive">تضمين غير النشطين</param>
        /// <returns>قائمة الرعية مع الديون</returns>
        Task<IEnumerable<Raayah>> GetWithWeeklyDebtsAsync(bool includeInactive = false);

        /// <summary>
        /// الحصول على رعوي مع ديونه الأسبوعية
        /// </summary>
        /// <param name="id">معرف الرعوي</param>
        /// <returns>بيانات الرعوي مع الديون</returns>
        Task<Raayah?> GetWithWeeklyDebtsByIdAsync(int id);

        /// <summary>
        /// تحديث إعدادات متعددة للرعية
        /// </summary>
        /// <param name="updates">قائمة التحديثات</param>
        /// <returns>عدد السجلات المحدثة</returns>
        Task<int> BulkUpdateSettingsAsync(IEnumerable<(int Id, bool EnableDiscount, bool InKashfOzri, bool InKharijKashf)> updates);

        /// <summary>
        /// حفظ التغييرات
        /// </summary>
        /// <returns>عدد السجلات المتأثرة</returns>
        Task<int> SaveChangesAsync();
    }
}
