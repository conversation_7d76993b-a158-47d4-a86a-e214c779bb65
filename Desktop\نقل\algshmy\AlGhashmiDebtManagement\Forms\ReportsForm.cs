using AlGhashmiDebtManagement.Models;
using AlGhashmiDebtManagement.Services;
using AlGhashmiDebtManagement.Helpers;
using System.Linq;
using System.Drawing.Printing;

namespace AlGhashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة التقارير والكشوفات
    /// </summary>
    public partial class ReportsForm : Form
    {
        private readonly ReportService _reportService;
        private readonly DebtCalculationService _calculationService;
        private readonly ExportService _exportService;
        private IEnumerable<ReportData>? _currentReportData;
        private ReportSummary? _currentSummary;
        private ReportType _currentReportType;
        private bool _isLoading = false;

        // متغيرات الطباعة
        private PrintDocument? _printDocument;
        private int _currentPrintPageIndex = 0;
        private List<string[]> _printData = new List<string[]>();



        /// <summary>
        /// منشئ نافذة التقارير
        /// </summary>
        public ReportsForm(ReportService reportService, DebtCalculationService calculationService, ExportService exportService)
        {
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _calculationService = calculationService ?? throw new ArgumentNullException(nameof(calculationService));
            _exportService = exportService ?? throw new ArgumentNullException(nameof(exportService));

            // تهيئة الطباعة
            InitializePrinting();

            InitializeComponent();
            SetupArabicUI();
            SetupReportTypes();
            SetupDefaultDates();
        }

        /// <summary>
        /// تهيئة إعدادات الطباعة
        /// </summary>
        private void InitializePrinting()
        {
            _printDocument = new PrintDocument();
            _printDocument.PrintPage += PrintDocument_PrintPage;
        }

        /// <summary>
        /// إعداد الواجهة العربية
        /// </summary>
        private void SetupArabicUI()
        {
            // إعداد اتجاه النص من اليمين إلى اليسار
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد الخط العربي
            this.Font = new Font("Tahoma", 12F, FontStyle.Regular);

            // إعداد عنوان النافذة
            this.Text = "التقارير والكشوفات - شركة الغشمي";
            this.StartPosition = FormStartPosition.CenterParent;

            // إعداد الألوان
            this.BackColor = Color.FromArgb(248, 249, 250);

            // إعداد DataGridView
            SetupDataGridView();
        }

        /// <summary>
        /// إعداد أنواع التقارير
        /// </summary>
        private void SetupReportTypes()
        {
            var reportTypes = _reportService.GetAvailableReportTypes();
            
            cmbReportType.DataSource = reportTypes.ToList();
            cmbReportType.DisplayMember = "Value";
            cmbReportType.ValueMember = "Key";
            cmbReportType.SelectedIndex = 0;
        }

        /// <summary>
        /// إعداد التواريخ الافتراضية
        /// </summary>
        private void SetupDefaultDates()
        {
            // تعيين بداية ونهاية الأسبوع الحالي
            var today = DateTime.Today;
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            dtpDateFrom.Value = startOfWeek;
            dtpDateTo.Value = endOfWeek;
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            dgvReport.AutoGenerateColumns = false;
            dgvReport.AllowUserToAddRows = false;
            dgvReport.AllowUserToDeleteRows = false;
            dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvReport.MultiSelect = false;
            dgvReport.ReadOnly = true;

            // إعداد الألوان
            dgvReport.BackgroundColor = Color.White;
            dgvReport.GridColor = Color.FromArgb(224, 224, 224);
            dgvReport.DefaultCellStyle.SelectionBackColor = Color.FromArgb(46, 134, 171);
            dgvReport.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvReport.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(46, 134, 171);
            dgvReport.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvReport.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            dgvReport.RowHeadersWidth = 30;

            // إضافة الأعمدة
            AddReportColumns();
        }

        /// <summary>
        /// إضافة أعمدة التقرير
        /// </summary>
        private void AddReportColumns()
        {
            // عمود اسم الرعوي
            dgvReport.Columns.Add(ColumnHelper.CreateTextColumn(
                ColumnHelper.Columns.Report.RaayahName,
                "RaayahName",
                ColumnHelper.Headers.Report.RaayahName,
                150,
                readOnly: true
            ));

            // أعمدة الفروع
            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.SamirAmount,
                "SamirAmount",
                ColumnHelper.Headers.Report.SamirAmount,
                100,
                readOnly: true,
                headerColor: Color.FromArgb(40, 167, 69)
            ));

            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.MaherAmount,
                "MaherAmount",
                ColumnHelper.Headers.Report.MaherAmount,
                100,
                readOnly: true,
                headerColor: Color.FromArgb(23, 162, 184)
            ));

            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.RaidAmount,
                "RaidAmount",
                ColumnHelper.Headers.Report.RaidAmount,
                100,
                readOnly: true,
                headerColor: Color.FromArgb(241, 143, 1)
            ));

            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.HaiderAmount,
                "HaiderAmount",
                ColumnHelper.Headers.Report.HaiderAmount,
                100,
                readOnly: true,
                headerColor: Color.FromArgb(162, 59, 114)
            ));

            // عمود المتأخر
            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.LateAmount,
                "LateAmount",
                ColumnHelper.Headers.Report.LateAmount,
                100,
                readOnly: true,
                headerColor: Color.FromArgb(220, 53, 69)
            ));

            // عمود الواصل
            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.ReceivedAmount,
                "ReceivedAmount",
                ColumnHelper.Headers.Report.ReceivedAmount,
                100,
                readOnly: true,
                headerColor: Color.FromArgb(108, 117, 125)
            ));

            // عمود إجمالي الديون
            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.TotalDebtAmount,
                "TotalDebtAmount",
                ColumnHelper.Headers.Report.TotalDebtAmount,
                120,
                readOnly: true,
                backgroundColor: Color.FromArgb(255, 248, 220)
            ));

            // عمود الصافي
            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.NetAmount,
                "NetAmount",
                ColumnHelper.Headers.Report.NetAmount,
                120,
                readOnly: true,
                backgroundColor: Color.FromArgb(220, 248, 255)
            ));

            // عمود خصم الحوالة (سيظهر حسب نوع التقرير)
            dgvReport.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.Report.DiscountAmount,
                "DiscountAmount",
                ColumnHelper.Headers.Report.DiscountAmount,
                120,
                readOnly: true,
                backgroundColor: Color.FromArgb(255, 220, 220)
            ));
            dgvReport.Columns[dgvReport.Columns.Count - 1].Visible = false;

            // تطبيق التنسيق العربي
            ColumnHelper.ApplyArabicFormatting(dgvReport);
        }



        /// <summary>
        /// توليد التقرير
        /// </summary>
        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                _isLoading = true;
                btnGenerateReport.Enabled = false;
                btnGenerateReport.Text = "جاري التوليد...";
                lblStatus.Text = "جاري توليد التقرير...";

                var dateFrom = dtpDateFrom.Value.Date;
                var dateTo = dtpDateTo.Value.Date;
                var reportType = (ReportType)cmbReportType.SelectedValue;

                // التحقق من صحة التواريخ
                if (dateTo < dateFrom)
                {
                    MessageBox.Show(
                        "تاريخ النهاية يجب أن يكون بعد تاريخ البداية",
                        "خطأ في التاريخ",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                // التحقق من وجود بيانات
                var hasData = await _reportService.HasDataInPeriodAsync(dateFrom, dateTo);
                if (!hasData)
                {
                    MessageBox.Show(
                        "لا توجد بيانات في الفترة المحددة",
                        "لا توجد بيانات",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                    return;
                }

                // توليد التقرير
                var criteria = new ReportCriteria
                {
                    DateFrom = dateFrom,
                    DateTo = dateTo,
                    ReportType = reportType,
                    IncludeInactive = false
                };

                var (data, summary) = await _reportService.GenerateReportAsync(criteria);
                
                _currentReportData = data;
                _currentSummary = summary;
                _currentReportType = reportType;

                // عرض البيانات
                DisplayReportData(data, summary, reportType);

                lblStatus.Text = $"تم توليد التقرير - {data.Count()} سجل";
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في توليد التقرير:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
                lblStatus.Text = "خطأ في توليد التقرير";
            }
            finally
            {
                _isLoading = false;
                btnGenerateReport.Enabled = true;
                btnGenerateReport.Text = "📊 توليد التقرير";
            }
        }

        /// <summary>
        /// عرض بيانات التقرير
        /// </summary>
        private void DisplayReportData(IEnumerable<ReportData> data, ReportSummary summary, ReportType reportType)
        {
            // إخفاء/إظهار عمود خصم الحوالة حسب نوع التقرير
            try
            {
                var discountColumn = dgvReport.Columns.Cast<DataGridViewColumn>()
                    .FirstOrDefault(c => c.DataPropertyName == "DiscountAmount");
                if (discountColumn != null)
                {
                    discountColumn.Visible = reportType == ReportType.ReportWithDiscount;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إظهار/إخفاء عمود خصم الحوالة: {ex.Message}");
            }

            // ربط البيانات
            dgvReport.DataSource = data.ToList();

            // تحديث الملخص
            UpdateSummaryDisplay(summary);

            // تحديث حالة أزرار التصدير والطباعة
            btnExportPdf.Enabled = data.Any();
            btnExportExcel.Enabled = data.Any();
            btnPrint.Enabled = data.Any();
            btnPrintPreview.Enabled = data.Any();
        }

        /// <summary>
        /// تحديث عرض الملخص
        /// </summary>
        private void UpdateSummaryDisplay(ReportSummary summary)
        {
            lblSummaryTitle.Text = $"ملخص التقرير - {summary.ReportPeriod}";
            lblTotalRaayah.Text = $"عدد الرعية: {summary.TotalRaayahCount}";
            lblTotalDebt.Text = $"إجمالي الديون: {summary.GrandTotalDebtAmount:N0}";
            lblTotalReceived.Text = $"إجمالي الواصل: {summary.TotalReceivedAmount:N0}";
            lblNetAmount.Text = $"الصافي: {summary.GrandNetAmount:N0}";
            
            if (summary.TotalDiscountAmount > 0)
            {
                lblDiscountAmount.Text = $"خصم الحوالة: {summary.TotalDiscountAmount:N0}";
                lblDiscountAmount.Visible = true;
                lblNetAfterDiscount.Text = $"الصافي بعد الخصم: {summary.GrandNetAfterDiscount:N0}";
                lblNetAfterDiscount.Visible = true;
            }
            else
            {
                lblDiscountAmount.Visible = false;
                lblNetAfterDiscount.Visible = false;
            }
        }

        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        private async void btnExportPdf_Click(object sender, EventArgs e)
        {
            if (_currentReportData == null || !_currentReportData.Any() || _currentSummary == null)
            {
                MessageBox.Show(
                    "لا توجد بيانات للتصدير",
                    "تنبيه",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                return;
            }

            try
            {
                // تصدير PDF غير مدعوم حالياً
                MessageBox.Show(
                    "تصدير PDF غير متاح حالياً.\n\nيمكنك استخدام تصدير Excel كبديل.",
                    "ميزة قيد التطوير",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تصدير PDF:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            finally
            {
                btnExportPdf.Enabled = true;
                btnExportPdf.Text = "📄 تصدير PDF";
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private async void btnExportExcel_Click(object sender, EventArgs e)
        {
            if (_currentReportData == null || !_currentReportData.Any() || _currentSummary == null)
            {
                MessageBox.Show(
                    "لا توجد بيانات للتصدير",
                    "تنبيه",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                return;
            }

            try
            {
                using var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = _exportService.GetDefaultExportPath(_currentReportType, "xlsx")
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    btnExportExcel.Enabled = false;
                    btnExportExcel.Text = "جاري التصدير...";

                    var success = await _exportService.ExportToExcelAsync(_currentReportData, _currentSummary, saveDialog.FileName);

                    if (success)
                    {
                        MessageBox.Show(
                            $"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}",
                            "نجح التصدير",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information
                        );

                        // فتح المجلد
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{saveDialog.FileName}\"");
                    }
                    else
                    {
                        MessageBox.Show(
                            "فشل في تصدير التقرير",
                            "خطأ في التصدير",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تصدير Excel:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            finally
            {
                btnExportExcel.Enabled = true;
                btnExportExcel.Text = "📋 تصدير Excel";
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentReportData == null || !_currentReportData.Any())
                {
                    MessageBox.Show(
                        "لا توجد بيانات للطباعة. يرجى إنشاء تقرير أولاً.",
                        "تنبيه",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                // تحضير البيانات للطباعة
                PreparePrintData();

                // إعداد الطباعة
                if (_printDocument != null)
                {
                    _printDocument.DocumentName = $"تقرير ديون الرعية - {DateTime.Now:dd/MM/yyyy}";

                    // إعداد حوار الطباعة المعياري
                    using var printDialog = new PrintDialog();
                    printDialog.Document = _printDocument;
                    printDialog.UseEXDialog = true; // استخدام الحوار المحسن
                    printDialog.AllowCurrentPage = true;
                    printDialog.AllowPrintToFile = true;
                    printDialog.AllowSelection = false;
                    printDialog.AllowSomePages = true;
                    printDialog.PrinterSettings = _printDocument.PrinterSettings;

                    // عرض حوار الطباعة
                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            // إعادة تعيين فهرس الصفحة
                            _currentPrintPageIndex = 0;

                            _printDocument.PrinterSettings = printDialog.PrinterSettings;
                            _printDocument.Print();

                            MessageBox.Show(
                                "تم إرسال التقرير للطباعة بنجاح",
                                "نجح",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information
                            );
                        }
                        catch (Exception printEx)
                        {
                            MessageBox.Show(
                                $"خطأ أثناء الطباعة:\n{printEx.Message}",
                                "خطأ في الطباعة",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Error
                            );
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في إعداد الطباعة:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void btnPrintPreview_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentReportData == null || !_currentReportData.Any())
                {
                    MessageBox.Show(
                        "لا توجد بيانات للمعاينة. يرجى إنشاء تقرير أولاً.",
                        "تنبيه",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                // تحضير البيانات للطباعة
                PreparePrintData();

                // إعداد الطباعة
                if (_printDocument != null)
                {
                    _printDocument.DocumentName = $"تقرير ديون الرعية - {DateTime.Now:dd/MM/yyyy}";

                    // إعادة تعيين فهرس الصفحة
                    _currentPrintPageIndex = 0;

                    // عرض معاينة الطباعة
                    using var printPreviewDialog = new PrintPreviewDialog();
                    printPreviewDialog.Document = _printDocument;
                    printPreviewDialog.RightToLeft = RightToLeft.Yes;
                    printPreviewDialog.RightToLeftLayout = true;
                    printPreviewDialog.Text = "معاينة الطباعة";
                    printPreviewDialog.WindowState = FormWindowState.Maximized;

                    printPreviewDialog.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في معاينة الطباعة:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// تحضير البيانات للطباعة
        /// </summary>
        private void PreparePrintData()
        {
            try
            {
                _printData.Clear();
                _currentPrintPageIndex = 0;

                if (_currentReportData == null || !_currentReportData.Any())
                {
                    MessageBox.Show("لا توجد بيانات تقرير للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إضافة رؤوس الأعمدة
                var headers = new[]
                {
                    ColumnHelper.Headers.Report.RaayahName,
                    ColumnHelper.Headers.Report.SamirAmount,
                    ColumnHelper.Headers.Report.MaherAmount,
                    ColumnHelper.Headers.Report.RaidAmount,
                    ColumnHelper.Headers.Report.HaiderAmount,
                    ColumnHelper.Headers.Report.LateAmount,
                    ColumnHelper.Headers.Report.ReceivedAmount,
                    ColumnHelper.Headers.Report.TotalDebtAmount,
                    ColumnHelper.Headers.Report.NetAmount
                };
                _printData.Add(headers);

                // إضافة بيانات التقرير
                foreach (var item in _currentReportData)
                {
                    var row = new[]
                    {
                        item.RaayahName ?? "",
                        item.SamirAmount.ToString("N0"),
                        item.MaherAmount.ToString("N0"),
                        item.RaidAmount.ToString("N0"),
                        item.HaiderAmount.ToString("N0"),
                        item.LateAmount.ToString("N0"),
                        item.ReceivedAmount.ToString("N0"),
                        item.TotalDebtAmount.ToString("N0"),
                        item.NetAmount.ToString("N0")
                    };
                    _printData.Add(row);
                }

                // إضافة صف الإجمالي
                if (_currentSummary != null)
                {
                    var summaryRow = new string[]
                    {
                        "الإجمالي",
                        _currentSummary.TotalSamirAmount.ToString("N0"),
                        _currentSummary.TotalMaherAmount.ToString("N0"),
                        _currentSummary.TotalRaidAmount.ToString("N0"),
                        _currentSummary.TotalHaiderAmount.ToString("N0"),
                        _currentSummary.TotalLateAmount.ToString("N0"),
                        _currentSummary.TotalReceivedAmount.ToString("N0"),
                        (_currentSummary.TotalSamirAmount + _currentSummary.TotalMaherAmount +
                         _currentSummary.TotalRaidAmount + _currentSummary.TotalHaiderAmount +
                         _currentSummary.TotalLateAmount).ToString("N0"),
                        (_currentSummary.TotalSamirAmount + _currentSummary.TotalMaherAmount +
                         _currentSummary.TotalRaidAmount + _currentSummary.TotalHaiderAmount +
                         _currentSummary.TotalLateAmount - _currentSummary.TotalReceivedAmount).ToString("N0")
                    };
                    _printData.Add(summaryRow);
                }

                // تشخيص: عرض عدد الصفوف المحضرة
                MessageBox.Show($"تم تحضير {_printData.Count} صف للطباعة", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحضير البيانات للطباعة:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج طباعة الصفحة
        /// </summary>
        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            try
            {
                var graphics = e.Graphics;
                var font = new Font("Tahoma", 10);
                var headerFont = new Font("Tahoma", 12, FontStyle.Bold);
                var titleFont = new Font("Tahoma", 16, FontStyle.Bold);

                var brush = Brushes.Black;
                var headerBrush = Brushes.DarkBlue;

                var pageWidth = e.PageBounds.Width;
                var pageHeight = e.PageBounds.Height;
                var margin = 50;

                var currentY = margin;

                // التحقق من وجود البيانات
                if (_printData == null || !_printData.Any())
                {
                    graphics.DrawString("لا توجد بيانات للطباعة", titleFont, brush, margin, currentY);
                    return;
                }

                // طباعة العنوان
                var title = "تقرير ديون الرعية - شركة الغشمي";
                var titleSize = graphics.MeasureString(title, titleFont);
                graphics.DrawString(title, titleFont, headerBrush,
                    (pageWidth - titleSize.Width) / 2, currentY);
                currentY += (int)titleSize.Height + 20;

                // طباعة التاريخ
                var dateText = $"التاريخ: {DateTime.Now:dd/MM/yyyy HH:mm}";
                graphics.DrawString(dateText, font, brush, margin, currentY);
                currentY += 30;

                // حساب عرض الأعمدة
                var columnCount = _printData.FirstOrDefault()?.Length ?? 0;
                if (columnCount == 0)
                {
                    graphics.DrawString("لا توجد أعمدة للطباعة", font, brush, margin, currentY);
                    return;
                }

                var availableWidth = pageWidth - (2 * margin);
                var columnWidth = availableWidth / columnCount;

                // طباعة البيانات
                var rowHeight = 25;
                var maxRowsPerPage = (int)((pageHeight - currentY - margin - 50) / rowHeight);

                // تحديد الصفوف للطباعة في هذه الصفحة
                var startRow = _currentPrintPageIndex;
                var endRow = Math.Min(startRow + maxRowsPerPage, _printData.Count);

                for (int i = startRow; i < endRow; i++)
                {
                    var row = _printData[i];

                    // تحديد الخط والفرشاة حسب نوع الصف
                    var currentFont = (i == 0 || i == _printData.Count - 1) ? headerFont : font;
                    var currentBrush = (i == 0) ? headerBrush : brush;

                    // البدء من اليمين للطباعة العربية الصحيحة
                    var currentX = pageWidth - margin - columnWidth;

                    for (int j = 0; j < row.Length; j++)
                    {
                        var cellText = row[j] ?? "";
                        var cellRect = new RectangleF(currentX, currentY, columnWidth, rowHeight);

                        // رسم حدود الخلية
                        graphics.DrawRectangle(Pens.Black, Rectangle.Round(cellRect));

                        // كتابة النص
                        var stringFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Center,
                            LineAlignment = StringAlignment.Center,
                            FormatFlags = StringFormatFlags.DirectionRightToLeft
                        };

                        graphics.DrawString(cellText, currentFont, currentBrush, cellRect, stringFormat);

                        // الانتقال إلى العمود التالي (من اليمين إلى اليسار)
                        currentX -= columnWidth;
                    }

                    currentY += rowHeight;
                }

                // تحديث فهرس الصفحة التالية
                _currentPrintPageIndex = endRow;
                e.HasMorePages = _currentPrintPageIndex < _printData.Count;

                // طباعة رقم الصفحة
                var pageNumber = $"صفحة {Math.Ceiling((double)endRow / maxRowsPerPage)}";
                var pageNumberSize = graphics.MeasureString(pageNumber, font);
                graphics.DrawString(pageNumber, font, brush,
                    (pageWidth - pageNumberSize.Width) / 2, pageHeight - margin);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الصفحة: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// معالج تغيير نوع التقرير
        /// </summary>
        private void cmbReportType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            // تحديث وصف التقرير
            var reportType = (ReportType)cmbReportType.SelectedValue;
            UpdateReportDescription(reportType);
        }



        /// <summary>
        /// تحديث وصف التقرير
        /// </summary>
        private void UpdateReportDescription(ReportType reportType)
        {
            var description = reportType switch
            {
                ReportType.FullReport => "يعرض جميع الرعية النشطين مع ديونهم في الفترة المحددة",
                ReportType.ReportWithDiscount => "يعرض فقط الرعية الذين لديهم خصم حوالة مفعل مع حساب الخصم",
                ReportType.CardsReport => "يعرض بطاقة منفصلة لكل رعوي مع تفاصيل ديونه",
                ReportType.KashfOzri => "يعرض فقط الرعية المحددين في كشف الأوزري",
                ReportType.KharijKashf => "يعرض فقط الرعية المحددين في خارج الكشف",
                _ => "تقرير عام"
            };

            lblReportDescription.Text = description;
        }
    }
}
