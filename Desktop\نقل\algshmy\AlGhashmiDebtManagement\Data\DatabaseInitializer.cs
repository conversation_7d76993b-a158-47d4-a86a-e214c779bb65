using Microsoft.EntityFrameworkCore;
using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Data
{
    /// <summary>
    /// فئة تهيئة قاعدة البيانات
    /// </summary>
    public static class DatabaseInitializer
    {
        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public static void Initialize(AppDbContext context)
        {
            try
            {
                // التحقق من حالة قاعدة البيانات
                bool databaseExists = context.Database.CanConnect();

                if (!databaseExists)
                {
                    // إنشاء قاعدة البيانات إذا لم تكن موجودة
                    context.Database.EnsureCreated();
                }
                else
                {
                    // التحقق من وجود migrations معلقة
                    var pendingMigrations = context.Database.GetPendingMigrations();
                    if (pendingMigrations.Any())
                    {
                        try
                        {
                            context.Database.Migrate();
                        }
                        catch (Exception migrationEx)
                        {
                            // في حالة فشل Migration، إعادة إنشاء قاعدة البيانات
                            System.Diagnostics.Debug.WriteLine($"فشل في تطبيق Migration: {migrationEx.Message}");
                            RecreateDatabase(context);
                            return;
                        }
                    }
                }

                // التحقق من سلامة الجداول
                if (!ValidateDatabaseStructure(context))
                {
                    RecreateDatabase(context);
                    return;
                }

                // إضافة بيانات أولية إذا كانت قاعدة البيانات فارغة
                SeedInitialData(context);
            }
            catch (Exception ex)
            {
                // في حالة أي خطأ، محاولة إعادة إنشاء قاعدة البيانات
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                try
                {
                    RecreateDatabase(context);
                }
                catch (Exception recreateEx)
                {
                    System.Diagnostics.Debug.WriteLine($"فشل في إعادة إنشاء قاعدة البيانات: {recreateEx.Message}");
                    throw new InvalidOperationException($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// إعادة إنشاء قاعدة البيانات من الصفر
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        private static void RecreateDatabase(AppDbContext context)
        {
            try
            {
                // حذف قاعدة البيانات الموجودة
                context.Database.EnsureDeleted();

                // إنشاء قاعدة البيانات الجديدة
                context.Database.EnsureCreated();

                // إضافة البيانات الأولية
                SeedInitialData(context);

                System.Diagnostics.Debug.WriteLine("تم إعادة إنشاء قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة إنشاء قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من سلامة هيكل قاعدة البيانات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        /// <returns>true إذا كان الهيكل سليم</returns>
        private static bool ValidateDatabaseStructure(AppDbContext context)
        {
            try
            {
                // محاولة الوصول للجداول الأساسية
                var raayahCount = context.Raayah.Count();
                var weeklyDebtsCount = context.WeeklyDebts.Count();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من هيكل قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة بيانات أولية لقاعدة البيانات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        private static void SeedInitialData(AppDbContext context)
        {
            // التحقق من وجود بيانات في جدول الرعية
            if (!context.Raayah.Any())
            {
                // إضافة بيانات أولية للرعية
                var initialRaayah = new List<Raayah>
                {
                    new Raayah
                    {
                        FullName = "أحمد محمد علي",
                        EnableDiscount = true,
                        InKashfOzri = true,
                        InKharijKashf = false,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        Notes = "رعوي تجريبي"
                    },
                    new Raayah
                    {
                        FullName = "علي حسن سالم",
                        EnableDiscount = false,
                        InKashfOzri = true,
                        InKharijKashf = false,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        Notes = "رعوي تجريبي"
                    },
                    new Raayah
                    {
                        FullName = "محمد سالم أحمد",
                        EnableDiscount = true,
                        InKashfOzri = false,
                        InKharijKashf = true,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        Notes = "رعوي تجريبي"
                    },
                    new Raayah
                    {
                        FullName = "سالم أحمد محمد",
                        EnableDiscount = false,
                        InKashfOzri = true,
                        InKharijKashf = false,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        Notes = "رعوي تجريبي"
                    },
                    new Raayah
                    {
                        FullName = "حسن علي محمد",
                        EnableDiscount = true,
                        InKashfOzri = false,
                        InKharijKashf = true,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        Notes = "رعوي تجريبي"
                    }
                };

                context.Raayah.AddRange(initialRaayah);
                context.SaveChanges();

                // إضافة بيانات تجريبية للديون الأسبوعية
                AddSampleWeeklyDebts(context);
            }
        }

        /// <summary>
        /// إضافة بيانات تجريبية للديون الأسبوعية
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        private static void AddSampleWeeklyDebts(AppDbContext context)
        {
            var raayahList = context.Raayah.ToList();
            if (raayahList.Any())
            {
                var random = new Random();
                var startDate = DateTime.Today.AddDays(-14); // أسبوعين مضيا

                for (int week = 0; week < 2; week++)
                {
                    var weekStart = startDate.AddDays(week * 7);
                    var weekEnd = weekStart.AddDays(6);

                    foreach (var raayah in raayahList)
                    {
                        var weeklyDebt = new WeeklyDebt
                        {
                            RaayahId = raayah.Id,
                            DateFrom = weekStart,
                            DateTo = weekEnd,
                            SamirAmount = random.Next(500, 2000),
                            MaherAmount = random.Next(500, 2000),
                            RaidAmount = random.Next(500, 2000),
                            HaiderAmount = random.Next(500, 2000),
                            LateAmount = random.Next(0, 500),
                            ReceivedAmount = random.Next(1000, 3000),
                            CreatedDate = DateTime.Now,
                            Notes = $"بيانات تجريبية للأسبوع {week + 1}"
                        };

                        context.WeeklyDebts.Add(weeklyDebt);
                    }
                }

                context.SaveChanges();
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم إنشاء النسخة الاحتياطية بنجاح</returns>
        public static bool CreateBackup(AppDbContext context, string? backupPath = null)
        {
            try
            {
                // الحصول على مسار قاعدة البيانات الحالية
                var connectionString = context.Database.GetConnectionString();
                var dbPath = ExtractDbPathFromConnectionString(connectionString);

                if (string.IsNullOrEmpty(dbPath) || !File.Exists(dbPath))
                {
                    return false;
                }

                // تحديد مسار النسخة الاحتياطية
                if (string.IsNullOrEmpty(backupPath))
                {
                    var backupDir = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "AlGhashmiDebtManagement",
                        "Backups"
                    );
                    Directory.CreateDirectory(backupDir);

                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    backupPath = Path.Combine(backupDir, $"AlGhashmiDebtManagement_Backup_{timestamp}.db");
                }

                // نسخ ملف قاعدة البيانات
                File.Copy(dbPath, backupPath, true);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// استخراج مسار قاعدة البيانات من نص الاتصال
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <returns>مسار قاعدة البيانات</returns>
        private static string? ExtractDbPathFromConnectionString(string? connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return null;

            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split('=');
                if (keyValue.Length == 2 && keyValue[0].Trim().ToLower() == "data source")
                {
                    return keyValue[1].Trim();
                }
            }

            return null;
        }

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تمت الاستعادة بنجاح</returns>
        public static bool RestoreFromBackup(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    return false;
                }

                // الحصول على مسار قاعدة البيانات الحالية
                using var context = new AppDbContext();
                var connectionString = context.Database.GetConnectionString();
                var dbPath = ExtractDbPathFromConnectionString(connectionString);

                if (string.IsNullOrEmpty(dbPath))
                {
                    return false;
                }

                // إغلاق الاتصال
                context.Dispose();

                // نسخ النسخة الاحتياطية إلى مكان قاعدة البيانات الحالية
                File.Copy(backupPath, dbPath, true);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }
    }
}
