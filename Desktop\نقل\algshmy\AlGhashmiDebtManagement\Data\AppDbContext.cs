using Microsoft.EntityFrameworkCore;
using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class AppDbContext : DbContext
    {
        /// <summary>
        /// جدول الرعية
        /// </summary>
        public DbSet<Raayah> Raayah { get; set; }

        /// <summary>
        /// جدول الديون الأسبوعية
        /// </summary>
        public DbSet<WeeklyDebt> WeeklyDebts { get; set; }

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public AppDbContext()
        {
        }

        /// <summary>
        /// منشئ مع خيارات
        /// </summary>
        /// <param name="options">خيارات قاعدة البيانات</param>
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// إعداد الاتصال بقاعدة البيانات
        /// </summary>
        /// <param name="optionsBuilder">منشئ الخيارات</param>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // مسار قاعدة البيانات
                string dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "AlGhashmiDebtManagement",
                    "AlGhashmiDebtManagement.db"
                );

                // إنشاء المجلد إذا لم يكن موجوداً
                Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);

                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        /// <summary>
        /// إعداد نماذج البيانات والعلاقات
        /// </summary>
        /// <param name="modelBuilder">منشئ النماذج</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // إعداد جدول الرعية
            modelBuilder.Entity<Raayah>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.FullName)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.Notes)
                    .HasMaxLength(500);

                entity.Property(e => e.CreatedDate)
                    .HasDefaultValueSql("datetime('now')");

                // إنشاء فهرس على الاسم
                entity.HasIndex(e => e.FullName)
                    .HasDatabaseName("IX_Raayah_FullName");

                // إنشاء فهرس على الحالة
                entity.HasIndex(e => e.IsActive)
                    .HasDatabaseName("IX_Raayah_IsActive");
            });

            // إعداد جدول الديون الأسبوعية
            modelBuilder.Entity<WeeklyDebt>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.SamirAmount)
                    .HasColumnType("decimal(18,2)")
                    .HasDefaultValue(0);

                entity.Property(e => e.MaherAmount)
                    .HasColumnType("decimal(18,2)")
                    .HasDefaultValue(0);

                entity.Property(e => e.RaidAmount)
                    .HasColumnType("decimal(18,2)")
                    .HasDefaultValue(0);

                entity.Property(e => e.HaiderAmount)
                    .HasColumnType("decimal(18,2)")
                    .HasDefaultValue(0);

                entity.Property(e => e.LateAmount)
                    .HasColumnType("decimal(18,2)")
                    .HasDefaultValue(0);

                entity.Property(e => e.ReceivedAmount)
                    .HasColumnType("decimal(18,2)")
                    .HasDefaultValue(0);

                entity.Property(e => e.Notes)
                    .HasMaxLength(500);

                entity.Property(e => e.CreatedDate)
                    .HasDefaultValueSql("datetime('now')");

                // إعداد العلاقة مع جدول الرعية
                entity.HasOne(d => d.Raayah)
                    .WithMany(p => p.WeeklyDebts)
                    .HasForeignKey(d => d.RaayahId)
                    .OnDelete(DeleteBehavior.Cascade);

                // إنشاء فهرس على معرف الرعوي
                entity.HasIndex(e => e.RaayahId)
                    .HasDatabaseName("IX_WeeklyDebts_RaayahId");

                // إنشاء فهرس على التواريخ
                entity.HasIndex(e => new { e.DateFrom, e.DateTo })
                    .HasDatabaseName("IX_WeeklyDebts_DateRange");

                // إنشاء فهرس مركب لمنع التكرار
                entity.HasIndex(e => new { e.RaayahId, e.DateFrom, e.DateTo })
                    .IsUnique()
                    .HasDatabaseName("IX_WeeklyDebts_Unique");
            });

            // إضافة بيانات أولية للرعية (اختيارية)
            SeedData(modelBuilder);
        }

        /// <summary>
        /// إضافة بيانات أولية
        /// </summary>
        /// <param name="modelBuilder">منشئ النماذج</param>
        private void SeedData(ModelBuilder modelBuilder)
        {
            // يمكن إضافة بيانات أولية هنا إذا لزم الأمر
            // مثال:
            /*
            modelBuilder.Entity<Raayah>().HasData(
                new Raayah
                {
                    Id = 1,
                    FullName = "أحمد محمد علي",
                    EnableDiscount = true,
                    InKashfOzri = true,
                    InKharijKashf = false,
                    CreatedDate = DateTime.Now,
                    IsActive = true
                }
            );
            */
        }

        /// <summary>
        /// حفظ التغييرات مع تحديث تاريخ التعديل
        /// </summary>
        /// <returns>عدد السجلات المتأثرة</returns>
        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        /// <summary>
        /// حفظ التغييرات بشكل غير متزامن مع تحديث تاريخ التعديل
        /// </summary>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>عدد السجلات المتأثرة</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// تحديث طوابع الوقت للكيانات المعدلة
        /// </summary>
        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is Raayah raayah)
                {
                    raayah.LastModifiedDate = DateTime.Now;
                }
                else if (entry.Entity is WeeklyDebt weeklyDebt)
                {
                    weeklyDebt.LastModifiedDate = DateTime.Now;
                }
            }
        }
    }
}
