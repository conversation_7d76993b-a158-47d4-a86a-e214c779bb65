using Microsoft.EntityFrameworkCore;
using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Data.Repositories
{
    /// <summary>
    /// تطبيق مستودع الديون الأسبوعية
    /// </summary>
    public class WeeklyDebtRepository : IWeeklyDebtRepository
    {
        private readonly AppDbContext _context;

        /// <summary>
        /// منشئ مستودع الديون الأسبوعية
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public WeeklyDebtRepository(AppDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// الحصول على جميع الديون الأسبوعية
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetAllAsync()
        {
            return await _context.WeeklyDebts
                .Include(w => w.<PERSON><PERSON><PERSON>)
                .OrderByDescending(w => w.DateFrom)
                .ThenBy(w => w.Raayah.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على دين أسبوعي بالمعرف
        /// </summary>
        public async Task<WeeklyDebt?> GetByIdAsync(int id)
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .FirstOrDefaultAsync(w => w.Id == id);
        }

        /// <summary>
        /// الحصول على الديون الأسبوعية لرعوي معين
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetByRaayahIdAsync(int raayahId)
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .Where(w => w.RaayahId == raayahId)
                .OrderByDescending(w => w.DateFrom)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الديون الأسبوعية في فترة زمنية
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetByDateRangeAsync(DateTime dateFrom, DateTime dateTo)
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .Where(w => w.DateFrom >= dateFrom && w.DateTo <= dateTo)
                .OrderBy(w => w.Raayah.FullName)
                .ThenBy(w => w.DateFrom)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الديون الأسبوعية لرعوي في فترة زمنية
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetByRaayahAndDateRangeAsync(int raayahId, DateTime dateFrom, DateTime dateTo)
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .Where(w => w.RaayahId == raayahId && w.DateFrom >= dateFrom && w.DateTo <= dateTo)
                .OrderBy(w => w.DateFrom)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الديون الأسبوعية لأسبوع محدد
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetByWeekAsync(DateTime weekStart, DateTime weekEnd)
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .Where(w => w.DateFrom == weekStart && w.DateTo == weekEnd)
                .OrderBy(w => w.Raayah.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على آخر الديون الأسبوعية لكل رعوي
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetLatestForEachRaayahAsync()
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .GroupBy(w => w.RaayahId)
                .Select(g => g.OrderByDescending(w => w.DateFrom).First())
                .OrderBy(w => w.Raayah.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الديون الأسبوعية مع بيانات الرعية
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> GetWithRaayahAsync(DateTime dateFrom, DateTime dateTo)
        {
            return await _context.WeeklyDebts
                .Include(w => w.Raayah)
                .Where(w => w.DateFrom >= dateFrom && w.DateTo <= dateTo && w.Raayah.IsActive)
                .OrderBy(w => w.Raayah.FullName)
                .ThenBy(w => w.DateFrom)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة دين أسبوعي جديد
        /// </summary>
        public async Task<WeeklyDebt> AddAsync(WeeklyDebt weeklyDebt)
        {
            if (weeklyDebt == null)
                throw new ArgumentNullException(nameof(weeklyDebt));

            weeklyDebt.CreatedDate = DateTime.Now;
            _context.WeeklyDebts.Add(weeklyDebt);
            await _context.SaveChangesAsync();
            return weeklyDebt;
        }

        /// <summary>
        /// إضافة ديون أسبوعية متعددة
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> AddRangeAsync(IEnumerable<WeeklyDebt> weeklyDebts)
        {
            if (weeklyDebts == null)
                throw new ArgumentNullException(nameof(weeklyDebts));

            var debtsList = weeklyDebts.ToList();
            foreach (var debt in debtsList)
            {
                debt.CreatedDate = DateTime.Now;
            }

            _context.WeeklyDebts.AddRange(debtsList);
            await _context.SaveChangesAsync();
            return debtsList;
        }

        /// <summary>
        /// تحديث دين أسبوعي
        /// </summary>
        public async Task<WeeklyDebt> UpdateAsync(WeeklyDebt weeklyDebt)
        {
            if (weeklyDebt == null)
                throw new ArgumentNullException(nameof(weeklyDebt));

            weeklyDebt.LastModifiedDate = DateTime.Now;
            _context.WeeklyDebts.Update(weeklyDebt);
            await _context.SaveChangesAsync();
            return weeklyDebt;
        }

        /// <summary>
        /// تحديث ديون أسبوعية متعددة
        /// </summary>
        public async Task<IEnumerable<WeeklyDebt>> UpdateRangeAsync(IEnumerable<WeeklyDebt> weeklyDebts)
        {
            if (weeklyDebts == null)
                throw new ArgumentNullException(nameof(weeklyDebts));

            var debtsList = weeklyDebts.ToList();
            foreach (var debt in debtsList)
            {
                debt.LastModifiedDate = DateTime.Now;
            }

            _context.WeeklyDebts.UpdateRange(debtsList);
            await _context.SaveChangesAsync();
            return debtsList;
        }

        /// <summary>
        /// حذف دين أسبوعي
        /// </summary>
        public async Task<bool> DeleteAsync(int id)
        {
            var weeklyDebt = await GetByIdAsync(id);
            if (weeklyDebt == null)
                return false;

            _context.WeeklyDebts.Remove(weeklyDebt);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف ديون أسبوعية متعددة
        /// </summary>
        public async Task<int> DeleteRangeAsync(IEnumerable<int> ids)
        {
            var debts = await _context.WeeklyDebts
                .Where(w => ids.Contains(w.Id))
                .ToListAsync();

            if (debts.Any())
            {
                _context.WeeklyDebts.RemoveRange(debts);
                await _context.SaveChangesAsync();
            }

            return debts.Count;
        }

        /// <summary>
        /// حذف جميع الديون الأسبوعية لرعوي
        /// </summary>
        public async Task<int> DeleteByRaayahIdAsync(int raayahId)
        {
            var debts = await _context.WeeklyDebts
                .Where(w => w.RaayahId == raayahId)
                .ToListAsync();

            if (debts.Any())
            {
                _context.WeeklyDebts.RemoveRange(debts);
                await _context.SaveChangesAsync();
            }

            return debts.Count;
        }

        /// <summary>
        /// حذف الديون الأسبوعية في فترة زمنية
        /// </summary>
        public async Task<int> DeleteByDateRangeAsync(DateTime dateFrom, DateTime dateTo)
        {
            var debts = await _context.WeeklyDebts
                .Where(w => w.DateFrom >= dateFrom && w.DateTo <= dateTo)
                .ToListAsync();

            if (debts.Any())
            {
                _context.WeeklyDebts.RemoveRange(debts);
                await _context.SaveChangesAsync();
            }

            return debts.Count;
        }

        /// <summary>
        /// التحقق من وجود ديون أسبوعية لرعوي في فترة زمنية
        /// </summary>
        public async Task<bool> ExistsAsync(int raayahId, DateTime dateFrom, DateTime dateTo, int? excludeId = null)
        {
            var query = _context.WeeklyDebts
                .Where(w => w.RaayahId == raayahId && w.DateFrom == dateFrom && w.DateTo == dateTo);

            if (excludeId.HasValue)
            {
                query = query.Where(w => w.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// الحصول على إجمالي الديون لرعوي في فترة زمنية
        /// </summary>
        public async Task<decimal> GetTotalDebtAsync(int raayahId, DateTime dateFrom, DateTime dateTo)
        {
            return await _context.WeeklyDebts
                .Where(w => w.RaayahId == raayahId && w.DateFrom >= dateFrom && w.DateTo <= dateTo)
                .SumAsync(w => w.TotalDebtAmount);
        }

        /// <summary>
        /// الحصول على إجمالي المبالغ الواصلة لرعوي في فترة زمنية
        /// </summary>
        public async Task<decimal> GetTotalReceivedAsync(int raayahId, DateTime dateFrom, DateTime dateTo)
        {
            return await _context.WeeklyDebts
                .Where(w => w.RaayahId == raayahId && w.DateFrom >= dateFrom && w.DateTo <= dateTo)
                .SumAsync(w => w.ReceivedAmount);
        }

        /// <summary>
        /// الحصول على إحصائيات الديون في فترة زمنية
        /// </summary>
        public async Task<(decimal TotalDebt, decimal TotalReceived, decimal NetAmount, int RecordsCount)> GetStatisticsAsync(DateTime dateFrom, DateTime dateTo)
        {
            var debts = await _context.WeeklyDebts
                .Where(w => w.DateFrom >= dateFrom && w.DateTo <= dateTo)
                .ToListAsync();

            var totalDebt = debts.Sum(w => w.TotalDebtAmount);
            var totalReceived = debts.Sum(w => w.ReceivedAmount);
            var netAmount = totalDebt - totalReceived;
            var recordsCount = debts.Count;

            return (totalDebt, totalReceived, netAmount, recordsCount);
        }

        /// <summary>
        /// حفظ التغييرات
        /// </summary>
        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }
    }
}
