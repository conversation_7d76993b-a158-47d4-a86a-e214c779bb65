using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Data.Repositories
{
    /// <summary>
    /// واجهة مستودع الديون الأسبوعية
    /// </summary>
    public interface IWeeklyDebtRepository
    {
        /// <summary>
        /// الحصول على جميع الديون الأسبوعية
        /// </summary>
        /// <returns>قائمة الديون الأسبوعية</returns>
        Task<IEnumerable<WeeklyDebt>> GetAllAsync();

        /// <summary>
        /// الحصول على دين أسبوعي بالمعرف
        /// </summary>
        /// <param name="id">معرف الدين الأسبوعي</param>
        /// <returns>بيانات الدين الأسبوعي أو null</returns>
        Task<WeeklyDebt?> GetByIdAsync(int id);

        /// <summary>
        /// الحصول على الديون الأسبوعية لرعوي معين
        /// </summary>
        /// <param name="raayahId">معرف الرعوي</param>
        /// <returns>قائمة الديون الأسبوعية للرعوي</returns>
        Task<IEnumerable<WeeklyDebt>> GetByRaayahIdAsync(int raayahId);

        /// <summary>
        /// الحصول على الديون الأسبوعية في فترة زمنية
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>قائمة الديون الأسبوعية في الفترة</returns>
        Task<IEnumerable<WeeklyDebt>> GetByDateRangeAsync(DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// الحصول على الديون الأسبوعية لرعوي في فترة زمنية
        /// </summary>
        /// <param name="raayahId">معرف الرعوي</param>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>قائمة الديون الأسبوعية للرعوي في الفترة</returns>
        Task<IEnumerable<WeeklyDebt>> GetByRaayahAndDateRangeAsync(int raayahId, DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// الحصول على الديون الأسبوعية لأسبوع محدد
        /// </summary>
        /// <param name="weekStart">بداية الأسبوع</param>
        /// <param name="weekEnd">نهاية الأسبوع</param>
        /// <returns>قائمة الديون الأسبوعية للأسبوع</returns>
        Task<IEnumerable<WeeklyDebt>> GetByWeekAsync(DateTime weekStart, DateTime weekEnd);

        /// <summary>
        /// الحصول على آخر الديون الأسبوعية لكل رعوي
        /// </summary>
        /// <returns>قائمة آخر الديون الأسبوعية</returns>
        Task<IEnumerable<WeeklyDebt>> GetLatestForEachRaayahAsync();

        /// <summary>
        /// الحصول على الديون الأسبوعية مع بيانات الرعية
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>قائمة الديون الأسبوعية مع بيانات الرعية</returns>
        Task<IEnumerable<WeeklyDebt>> GetWithRaayahAsync(DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// إضافة دين أسبوعي جديد
        /// </summary>
        /// <param name="weeklyDebt">بيانات الدين الأسبوعي</param>
        /// <returns>الدين الأسبوعي المضاف</returns>
        Task<WeeklyDebt> AddAsync(WeeklyDebt weeklyDebt);

        /// <summary>
        /// إضافة ديون أسبوعية متعددة
        /// </summary>
        /// <param name="weeklyDebts">قائمة الديون الأسبوعية</param>
        /// <returns>قائمة الديون الأسبوعية المضافة</returns>
        Task<IEnumerable<WeeklyDebt>> AddRangeAsync(IEnumerable<WeeklyDebt> weeklyDebts);

        /// <summary>
        /// تحديث دين أسبوعي
        /// </summary>
        /// <param name="weeklyDebt">بيانات الدين الأسبوعي المحدثة</param>
        /// <returns>الدين الأسبوعي المحدث</returns>
        Task<WeeklyDebt> UpdateAsync(WeeklyDebt weeklyDebt);

        /// <summary>
        /// تحديث ديون أسبوعية متعددة
        /// </summary>
        /// <param name="weeklyDebts">قائمة الديون الأسبوعية المحدثة</param>
        /// <returns>قائمة الديون الأسبوعية المحدثة</returns>
        Task<IEnumerable<WeeklyDebt>> UpdateRangeAsync(IEnumerable<WeeklyDebt> weeklyDebts);

        /// <summary>
        /// حذف دين أسبوعي
        /// </summary>
        /// <param name="id">معرف الدين الأسبوعي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// حذف ديون أسبوعية متعددة
        /// </summary>
        /// <param name="ids">قائمة معرفات الديون الأسبوعية</param>
        /// <returns>عدد الديون المحذوفة</returns>
        Task<int> DeleteRangeAsync(IEnumerable<int> ids);

        /// <summary>
        /// حذف جميع الديون الأسبوعية لرعوي
        /// </summary>
        /// <param name="raayahId">معرف الرعوي</param>
        /// <returns>عدد الديون المحذوفة</returns>
        Task<int> DeleteByRaayahIdAsync(int raayahId);

        /// <summary>
        /// حذف الديون الأسبوعية في فترة زمنية
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>عدد الديون المحذوفة</returns>
        Task<int> DeleteByDateRangeAsync(DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// التحقق من وجود ديون أسبوعية لرعوي في فترة زمنية
        /// </summary>
        /// <param name="raayahId">معرف الرعوي</param>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <param name="excludeId">معرف الدين المستثنى من التحقق</param>
        /// <returns>true إذا كانت الديون موجودة</returns>
        Task<bool> ExistsAsync(int raayahId, DateTime dateFrom, DateTime dateTo, int? excludeId = null);

        /// <summary>
        /// الحصول على إجمالي الديون لرعوي في فترة زمنية
        /// </summary>
        /// <param name="raayahId">معرف الرعوي</param>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>إجمالي الديون</returns>
        Task<decimal> GetTotalDebtAsync(int raayahId, DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// الحصول على إجمالي المبالغ الواصلة لرعوي في فترة زمنية
        /// </summary>
        /// <param name="raayahId">معرف الرعوي</param>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>إجمالي المبالغ الواصلة</returns>
        Task<decimal> GetTotalReceivedAsync(int raayahId, DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// الحصول على إحصائيات الديون في فترة زمنية
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>إحصائيات الديون</returns>
        Task<(decimal TotalDebt, decimal TotalReceived, decimal NetAmount, int RecordsCount)> GetStatisticsAsync(DateTime dateFrom, DateTime dateTo);

        /// <summary>
        /// حفظ التغييرات
        /// </summary>
        /// <returns>عدد السجلات المتأثرة</returns>
        Task<int> SaveChangesAsync();
    }
}
