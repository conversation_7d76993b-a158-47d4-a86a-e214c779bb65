using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AlGhashmiDebtManagement.Models
{
    /// <summary>
    /// نموذج بيانات الرعية
    /// </summary>
    public class Raayah
    {
        /// <summary>
        /// المعرف الفريد للرعوي
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// الاسم الكامل للرعوي
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم لا يجب أن يتجاوز 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// تفعيل خصم الحوالة (3%)
        /// </summary>
        public bool EnableDiscount { get; set; } = false;

        /// <summary>
        /// إظهار الرعوي في كشف الأوزري
        /// </summary>
        public bool InKashfOzri { get; set; } = true;

        /// <summary>
        /// إظهار الرعوي في خارج الكشف
        /// </summary>
        public bool InKharijKashf { get; set; } = false;

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// حالة الرعوي (نشط/غير نشط)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string? Notes { get; set; }

        // Navigation Property
        /// <summary>
        /// قائمة الديون الأسبوعية للرعوي
        /// </summary>
        public virtual ICollection<WeeklyDebt> WeeklyDebts { get; set; } = new List<WeeklyDebt>();

        /// <summary>
        /// الاسم المعروض (للواجهة)
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{FullName} {(IsActive ? "" : "(غير نشط)")}";

        /// <summary>
        /// حالة خصم الحوالة (للواجهة)
        /// </summary>
        [NotMapped]
        public string DiscountStatus => EnableDiscount ? "مفعل" : "معطل";

        /// <summary>
        /// حالة كشف الأوزري (للواجهة)
        /// </summary>
        [NotMapped]
        public string KashfOzriStatus => InKashfOzri ? "نعم" : "لا";

        /// <summary>
        /// حالة خارج الكشف (للواجهة)
        /// </summary>
        [NotMapped]
        public string KharijKashfStatus => InKharijKashf ? "نعم" : "لا";

        public override string ToString()
        {
            return FullName;
        }
    }
}
