﻿// <auto-generated />
using System;
using AlGhashmiDebtManagement.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AlGhashmiDebtManagement.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("AlGhashmiDebtManagement.Models.Raayah", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<bool>("EnableDiscount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("InKashfOzri")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("InKharijKashf")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModifiedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("FullName")
                        .HasDatabaseName("IX_Raayah_FullName");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Raayah_IsActive");

                    b.ToTable("Raayah");
                });

            modelBuilder.Entity("AlGhashmiDebtManagement.Models.WeeklyDebt", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<DateTime>("DateFrom")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTo")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("HaiderAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime?>("LastModifiedDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("LateAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("MaherAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("RaayahId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("RaidAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("ReceivedAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("SamirAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("RaayahId")
                        .HasDatabaseName("IX_WeeklyDebts_RaayahId");

                    b.HasIndex("DateFrom", "DateTo")
                        .HasDatabaseName("IX_WeeklyDebts_DateRange");

                    b.HasIndex("RaayahId", "DateFrom", "DateTo")
                        .IsUnique()
                        .HasDatabaseName("IX_WeeklyDebts_Unique");

                    b.ToTable("WeeklyDebts");
                });

            modelBuilder.Entity("AlGhashmiDebtManagement.Models.WeeklyDebt", b =>
                {
                    b.HasOne("AlGhashmiDebtManagement.Models.Raayah", "Raayah")
                        .WithMany("WeeklyDebts")
                        .HasForeignKey("RaayahId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Raayah");
                });

            modelBuilder.Entity("AlGhashmiDebtManagement.Models.Raayah", b =>
                {
                    b.Navigation("WeeklyDebts");
                });
#pragma warning restore 612, 618
        }
    }
}
