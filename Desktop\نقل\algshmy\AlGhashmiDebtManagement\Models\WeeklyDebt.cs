using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AlGhashmiDebtManagement.Models
{
    /// <summary>
    /// نموذج بيانات الديون الأسبوعية
    /// </summary>
    public class WeeklyDebt
    {
        /// <summary>
        /// المعرف الفريد للدين الأسبوعي
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف الرعوي (Foreign Key)
        /// </summary>
        [Required]
        public int RaayahId { get; set; }

        /// <summary>
        /// تاريخ بداية الأسبوع
        /// </summary>
        [Required(ErrorMessage = "تاريخ بداية الأسبوع مطلوب")]
        public DateTime DateFrom { get; set; }

        /// <summary>
        /// تاريخ نهاية الأسبوع
        /// </summary>
        [Required(ErrorMessage = "تاريخ نهاية الأسبوع مطلوب")]
        public DateTime DateTo { get; set; }

        /// <summary>
        /// مبلغ فرع سمير
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ سمير يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SamirAmount { get; set; } = 0;

        /// <summary>
        /// مبلغ فرع ماهر
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ ماهر يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MaherAmount { get; set; } = 0;

        /// <summary>
        /// مبلغ فرع رايد
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ رايد يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal RaidAmount { get; set; } = 0;

        /// <summary>
        /// مبلغ فرع حيدر
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ حيدر يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal HaiderAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المتأخر
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المتأخر يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal LateAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الواصل
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ الواصل يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal ReceivedAmount { get; set; } = 0;

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string? Notes { get; set; }

        // Navigation Property
        /// <summary>
        /// بيانات الرعوي المرتبط
        /// </summary>
        [ForeignKey("RaayahId")]
        public virtual Raayah Raayah { get; set; } = null!;

        // Calculated Properties
        /// <summary>
        /// إجمالي ديون الفروع
        /// </summary>
        [NotMapped]
        public decimal TotalBranchesAmount => SamirAmount + MaherAmount + RaidAmount + HaiderAmount;

        /// <summary>
        /// إجمالي الديون (الفروع + المتأخر)
        /// </summary>
        [NotMapped]
        public decimal TotalDebtAmount => TotalBranchesAmount + LateAmount;

        /// <summary>
        /// الصافي (إجمالي الديون - الواصل)
        /// </summary>
        [NotMapped]
        public decimal NetAmount => TotalDebtAmount - ReceivedAmount;

        /// <summary>
        /// خصم الحوالة (3% من الصافي)
        /// </summary>
        [NotMapped]
        public decimal DiscountAmount => NetAmount * 0.03m;

        /// <summary>
        /// الصافي بعد الخصم
        /// </summary>
        [NotMapped]
        public decimal NetAfterDiscount => NetAmount - DiscountAmount;

        /// <summary>
        /// فترة الأسبوع (للعرض)
        /// </summary>
        [NotMapped]
        public string WeekPeriod => $"{DateFrom:dd/MM/yyyy} - {DateTo:dd/MM/yyyy}";

        /// <summary>
        /// التحقق من صحة التواريخ
        /// </summary>
        public bool IsValidDateRange()
        {
            return DateTo > DateFrom && (DateTo - DateFrom).Days <= 7;
        }

        public override string ToString()
        {
            return $"{Raayah?.FullName} - {WeekPeriod}";
        }
    }
}
