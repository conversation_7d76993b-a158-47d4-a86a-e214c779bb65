using AlGhashmiDebtManagement.Data.Repositories;
using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Services
{
    /// <summary>
    /// خدمة توليد التقارير
    /// </summary>
    public class ReportService
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtRepository _weeklyDebtRepository;
        private readonly DebtCalculationService _calculationService;

        /// <summary>
        /// منشئ خدمة التقارير
        /// </summary>
        public ReportService(IRaayahRepository raayahRepository, 
                           IWeeklyDebtRepository weeklyDebtRepository,
                           DebtCalculationService calculationService)
        {
            _raayahRepository = raayahRepository ?? throw new ArgumentNullException(nameof(raayahRepository));
            _weeklyDebtRepository = weeklyDebtRepository ?? throw new ArgumentNullException(nameof(weeklyDebtRepository));
            _calculationService = calculationService ?? throw new ArgumentNullException(nameof(calculationService));
        }

        /// <summary>
        /// توليد التقرير حسب المعايير المحددة
        /// </summary>
        /// <param name="criteria">معايير التقرير</param>
        /// <returns>بيانات التقرير والملخص</returns>
        public async Task<(IEnumerable<ReportData> Data, ReportSummary Summary)> GenerateReportAsync(ReportCriteria criteria)
        {
            if (criteria == null || !_calculationService.ValidateReportCriteria(criteria))
                throw new ArgumentException("معايير التقرير غير صحيحة");

            try
            {
                // تحميل البيانات الأساسية
                var weeklyDebts = await _weeklyDebtRepository.GetByDateRangeAsync(criteria.DateFrom, criteria.DateTo);
                var allRaayah = await _raayahRepository.GetAllAsync(criteria.IncludeInactive);

                // تحويل إلى بيانات تقرير
                var reportData = _calculationService.ConvertToReportData(weeklyDebts, allRaayah);

                // تصفية حسب نوع التقرير
                var filteredData = _calculationService.FilterReportData(reportData, criteria.ReportType);

                // حساب الملخص
                var summary = _calculationService.CalculateTotals(
                    weeklyDebts.Where(d => filteredData.Any(f => f.RaayahId == d.RaayahId)), 
                    allRaayah
                );

                // إعداد معلومات الملخص
                summary.ReportPeriod = criteria.ReportPeriod;
                summary.ReportType = criteria.ReportType;
                summary.GeneratedDate = DateTime.Now;

                return (filteredData, summary);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في توليد التقرير: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// توليد التقرير الكامل
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>التقرير الكامل</returns>
        public async Task<(IEnumerable<ReportData> Data, ReportSummary Summary)> GenerateFullReportAsync(DateTime dateFrom, DateTime dateTo)
        {
            var criteria = new ReportCriteria
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                ReportType = ReportType.FullReport,
                IncludeInactive = false
            };

            return await GenerateReportAsync(criteria);
        }

        /// <summary>
        /// توليد تقرير خصم الحوالة
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>تقرير خصم الحوالة</returns>
        public async Task<(IEnumerable<ReportData> Data, ReportSummary Summary)> GenerateDiscountReportAsync(DateTime dateFrom, DateTime dateTo)
        {
            var criteria = new ReportCriteria
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                ReportType = ReportType.ReportWithDiscount,
                IncludeInactive = false
            };

            return await GenerateReportAsync(criteria);
        }

        /// <summary>
        /// توليد كشف الأوزري
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>كشف الأوزري</returns>
        public async Task<(IEnumerable<ReportData> Data, ReportSummary Summary)> GenerateKashfOzriAsync(DateTime dateFrom, DateTime dateTo)
        {
            var criteria = new ReportCriteria
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                ReportType = ReportType.KashfOzri,
                IncludeInactive = false
            };

            return await GenerateReportAsync(criteria);
        }

        /// <summary>
        /// توليد كشف خارج الكشف
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>كشف خارج الكشف</returns>
        public async Task<(IEnumerable<ReportData> Data, ReportSummary Summary)> GenerateKharijKashfAsync(DateTime dateFrom, DateTime dateTo)
        {
            var criteria = new ReportCriteria
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                ReportType = ReportType.KharijKashf,
                IncludeInactive = false
            };

            return await GenerateReportAsync(criteria);
        }

        /// <summary>
        /// توليد كشف البطاقات
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>كشف البطاقات</returns>
        public async Task<IEnumerable<CardReportData>> GenerateCardsReportAsync(DateTime dateFrom, DateTime dateTo)
        {
            try
            {
                var criteria = new ReportCriteria
                {
                    DateFrom = dateFrom,
                    DateTo = dateTo,
                    ReportType = ReportType.CardsReport,
                    IncludeInactive = false
                };

                var (data, _) = await GenerateReportAsync(criteria);
                
                var cardsData = data.Select(d => new CardReportData
                {
                    RaayahId = d.RaayahId,
                    RaayahName = d.RaayahName,
                    SamirAmount = d.SamirAmount,
                    MaherAmount = d.MaherAmount,
                    RaidAmount = d.RaidAmount,
                    HaiderAmount = d.HaiderAmount,
                    LateAmount = d.LateAmount,
                    ReceivedAmount = d.ReceivedAmount,
                    EnableDiscount = d.EnableDiscount,
                    InKashfOzri = d.InKashfOzri,
                    InKharijKashf = d.InKharijKashf,
                    ReportPeriod = criteria.ReportPeriod,
                    ReportDate = DateTime.Now
                });

                return cardsData;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في توليد كشف البطاقات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات سريعة
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>الإحصائيات السريعة</returns>
        public async Task<Dictionary<string, object>> GetQuickStatsAsync(DateTime dateFrom, DateTime dateTo)
        {
            try
            {
                var (data, summary) = await GenerateFullReportAsync(dateFrom, dateTo);
                var percentages = _calculationService.CalculateBranchPercentages(data);

                return new Dictionary<string, object>
                {
                    ["TotalRaayah"] = summary.TotalRaayahCount,
                    ["TotalDebt"] = summary.GrandTotalDebtAmount,
                    ["TotalReceived"] = summary.TotalReceivedAmount,
                    ["NetAmount"] = summary.GrandNetAmount,
                    ["TotalDiscount"] = summary.TotalDiscountAmount,
                    ["BranchPercentages"] = percentages,
                    ["TopRaayah"] = data.OrderByDescending(d => d.TotalDebtAmount).Take(5).ToList(),
                    ["GeneratedAt"] = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في الحصول على الإحصائيات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// مقارنة فترتين زمنيتين
        /// </summary>
        /// <param name="period1From">بداية الفترة الأولى</param>
        /// <param name="period1To">نهاية الفترة الأولى</param>
        /// <param name="period2From">بداية الفترة الثانية</param>
        /// <param name="period2To">نهاية الفترة الثانية</param>
        /// <returns>مقارنة الفترتين</returns>
        public async Task<Dictionary<string, object>> ComparePeriods(DateTime period1From, DateTime period1To, 
                                                                   DateTime period2From, DateTime period2To)
        {
            try
            {
                var (data1, summary1) = await GenerateFullReportAsync(period1From, period1To);
                var (data2, summary2) = await GenerateFullReportAsync(period2From, period2To);

                var comparison = new Dictionary<string, object>
                {
                    ["Period1"] = new
                    {
                        Period = $"{period1From:dd/MM/yyyy} - {period1To:dd/MM/yyyy}",
                        TotalDebt = summary1.GrandTotalDebtAmount,
                        TotalReceived = summary1.TotalReceivedAmount,
                        NetAmount = summary1.GrandNetAmount,
                        RaayahCount = summary1.TotalRaayahCount
                    },
                    ["Period2"] = new
                    {
                        Period = $"{period2From:dd/MM/yyyy} - {period2To:dd/MM/yyyy}",
                        TotalDebt = summary2.GrandTotalDebtAmount,
                        TotalReceived = summary2.TotalReceivedAmount,
                        NetAmount = summary2.GrandNetAmount,
                        RaayahCount = summary2.TotalRaayahCount
                    },
                    ["Changes"] = new
                    {
                        DebtChange = summary2.GrandTotalDebtAmount - summary1.GrandTotalDebtAmount,
                        ReceivedChange = summary2.TotalReceivedAmount - summary1.TotalReceivedAmount,
                        NetChange = summary2.GrandNetAmount - summary1.GrandNetAmount,
                        RaayahChange = summary2.TotalRaayahCount - summary1.TotalRaayahCount
                    },
                    ["GeneratedAt"] = DateTime.Now
                };

                return comparison;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في مقارنة الفترات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على أنواع التقارير المتاحة
        /// </summary>
        /// <returns>قائمة أنواع التقارير</returns>
        public Dictionary<ReportType, string> GetAvailableReportTypes()
        {
            return new Dictionary<ReportType, string>
            {
                [ReportType.FullReport] = "التقرير الكامل",
                [ReportType.ReportWithDiscount] = "التقرير مع خصم الحوالة",
                [ReportType.CardsReport] = "كشف بطاقات",
                [ReportType.KashfOzri] = "كشف الأوزري",
                [ReportType.KharijKashf] = "خارج الكشف"
            };
        }

        /// <summary>
        /// التحقق من وجود بيانات في الفترة المحددة
        /// </summary>
        /// <param name="dateFrom">تاريخ البداية</param>
        /// <param name="dateTo">تاريخ النهاية</param>
        /// <returns>true إذا كانت هناك بيانات</returns>
        public async Task<bool> HasDataInPeriodAsync(DateTime dateFrom, DateTime dateTo)
        {
            try
            {
                var stats = await _weeklyDebtRepository.GetStatisticsAsync(dateFrom, dateTo);
                return stats.RecordsCount > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على آخر فترة بها بيانات
        /// </summary>
        /// <returns>آخر فترة أو null</returns>
        public async Task<(DateTime? DateFrom, DateTime? DateTo)?> GetLastDataPeriodAsync()
        {
            try
            {
                var latestDebts = await _weeklyDebtRepository.GetLatestForEachRaayahAsync();
                if (!latestDebts.Any())
                    return null;

                var latestDebt = latestDebts.OrderByDescending(d => d.DateTo).First();
                return (latestDebt.DateFrom, latestDebt.DateTo);
            }
            catch
            {
                return null;
            }
        }
    }
}
