namespace AlGhashmiDebtManagement.Forms
{
    partial class WeeklyDataEntryForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.pnlDateSelection = new Panel();
            this.lblDateFrom = new Label();
            this.dtpDateFrom = new DateTimePicker();
            this.lblDateTo = new Label();
            this.dtpDateTo = new DateTimePicker();
            this.lblWeekInfo = new Label();
            this.pnlMain = new Panel();
            this.dgvWeeklyData = new DataGridView();
            this.pnlTotals = new Panel();
            this.lblTotalsTitle = new Label();
            this.lblTotalSamir = new Label();
            this.lblTotalMaher = new Label();
            this.lblTotalRaid = new Label();
            this.lblTotalHaider = new Label();
            this.lblTotalLate = new Label();
            this.lblTotalReceived = new Label();
            this.lblGrandTotal = new Label();
            this.lblGrandNet = new Label();
            this.pnlButtons = new Panel();
            this.btnSave = new Button();
            this.btnClear = new Button();
            this.btnRefresh = new Button();
            this.btnClose = new Button();
            this.pnlFooter = new Panel();
            this.lblStatus = new Label();

            this.pnlHeader.SuspendLayout();
            this.pnlDateSelection.SuspendLayout();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvWeeklyData)).BeginInit();
            this.pnlTotals.SuspendLayout();
            this.pnlButtons.SuspendLayout();
            this.pnlFooter.SuspendLayout();
            this.SuspendLayout();

            // 
            // pnlHeader
            // 
            this.pnlHeader.BackColor = Color.FromArgb(46, 134, 171);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Location = new Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new Size(1200, 60);
            this.pnlHeader.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 18);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(250, 27);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "إدخال البيانات الأسبوعية";

            // 
            // pnlDateSelection
            // 
            this.pnlDateSelection.BackColor = Color.White;
            this.pnlDateSelection.BorderStyle = BorderStyle.FixedSingle;
            this.pnlDateSelection.Controls.Add(this.lblDateFrom);
            this.pnlDateSelection.Controls.Add(this.dtpDateFrom);
            this.pnlDateSelection.Controls.Add(this.lblDateTo);
            this.pnlDateSelection.Controls.Add(this.dtpDateTo);
            this.pnlDateSelection.Controls.Add(this.lblWeekInfo);
            this.pnlDateSelection.Dock = DockStyle.Top;
            this.pnlDateSelection.Location = new Point(0, 60);
            this.pnlDateSelection.Name = "pnlDateSelection";
            this.pnlDateSelection.Padding = new Padding(20, 10, 20, 10);
            this.pnlDateSelection.Size = new Size(1200, 70);
            this.pnlDateSelection.TabIndex = 1;

            // 
            // lblDateFrom
            // 
            this.lblDateFrom.AutoSize = true;
            this.lblDateFrom.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblDateFrom.Location = new Point(25, 25);
            this.lblDateFrom.Name = "lblDateFrom";
            this.lblDateFrom.Size = new Size(70, 19);
            this.lblDateFrom.TabIndex = 0;
            this.lblDateFrom.Text = "من تاريخ:";

            // 
            // dtpDateFrom
            // 
            this.dtpDateFrom.Font = new Font("Tahoma", 12F);
            this.dtpDateFrom.Format = DateTimePickerFormat.Short;
            this.dtpDateFrom.Location = new Point(110, 22);
            this.dtpDateFrom.Name = "dtpDateFrom";
            this.dtpDateFrom.Size = new Size(150, 27);
            this.dtpDateFrom.TabIndex = 1;

            // 
            // lblDateTo
            // 
            this.lblDateTo.AutoSize = true;
            this.lblDateTo.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblDateTo.Location = new Point(290, 25);
            this.lblDateTo.Name = "lblDateTo";
            this.lblDateTo.Size = new Size(70, 19);
            this.lblDateTo.TabIndex = 2;
            this.lblDateTo.Text = "إلى تاريخ:";

            // 
            // dtpDateTo
            // 
            this.dtpDateTo.Font = new Font("Tahoma", 12F);
            this.dtpDateTo.Format = DateTimePickerFormat.Short;
            this.dtpDateTo.Location = new Point(375, 22);
            this.dtpDateTo.Name = "dtpDateTo";
            this.dtpDateTo.Size = new Size(150, 27);
            this.dtpDateTo.TabIndex = 3;

            // 
            // lblWeekInfo
            // 
            this.lblWeekInfo.AutoSize = true;
            this.lblWeekInfo.Font = new Font("Tahoma", 12F);
            this.lblWeekInfo.ForeColor = Color.FromArgb(108, 117, 125);
            this.lblWeekInfo.Location = new Point(550, 25);
            this.lblWeekInfo.Name = "lblWeekInfo";
            this.lblWeekInfo.Size = new Size(200, 19);
            this.lblWeekInfo.TabIndex = 4;
            this.lblWeekInfo.Text = "📅 يجب أن تكون الفترة 7 أيام بالضبط";

            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.dgvWeeklyData);
            this.pnlMain.Controls.Add(this.pnlTotals);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Location = new Point(0, 130);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new Padding(10);
            this.pnlMain.Size = new Size(1200, 470);
            this.pnlMain.TabIndex = 2;

            // 
            // dgvWeeklyData
            // 
            this.dgvWeeklyData.AllowUserToAddRows = false;
            this.dgvWeeklyData.AllowUserToDeleteRows = false;
            this.dgvWeeklyData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvWeeklyData.BackgroundColor = Color.White;
            this.dgvWeeklyData.BorderStyle = BorderStyle.Fixed3D;
            this.dgvWeeklyData.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvWeeklyData.Dock = DockStyle.Fill;
            this.dgvWeeklyData.GridColor = Color.FromArgb(224, 224, 224);
            this.dgvWeeklyData.Location = new Point(10, 10);
            this.dgvWeeklyData.MultiSelect = false;
            this.dgvWeeklyData.Name = "dgvWeeklyData";
            this.dgvWeeklyData.RowHeadersWidth = 30;
            this.dgvWeeklyData.SelectionMode = DataGridViewSelectionMode.CellSelect;
            this.dgvWeeklyData.Size = new Size(980, 450);
            this.dgvWeeklyData.TabIndex = 0;
            this.dgvWeeklyData.CellValueChanged += new DataGridViewCellEventHandler(this.dgvWeeklyData_CellValueChanged);
            this.dgvWeeklyData.CellBeginEdit += new DataGridViewCellCancelEventHandler(this.dgvWeeklyData_CellBeginEdit);

            // 
            // pnlTotals
            // 
            this.pnlTotals.BackColor = Color.White;
            this.pnlTotals.BorderStyle = BorderStyle.FixedSingle;
            this.pnlTotals.Controls.Add(this.lblTotalsTitle);
            this.pnlTotals.Controls.Add(this.lblTotalSamir);
            this.pnlTotals.Controls.Add(this.lblTotalMaher);
            this.pnlTotals.Controls.Add(this.lblTotalRaid);
            this.pnlTotals.Controls.Add(this.lblTotalHaider);
            this.pnlTotals.Controls.Add(this.lblTotalLate);
            this.pnlTotals.Controls.Add(this.lblTotalReceived);
            this.pnlTotals.Controls.Add(this.lblGrandTotal);
            this.pnlTotals.Controls.Add(this.lblGrandNet);
            this.pnlTotals.Dock = DockStyle.Right;
            this.pnlTotals.Location = new Point(990, 10);
            this.pnlTotals.Name = "pnlTotals";
            this.pnlTotals.Padding = new Padding(15);
            this.pnlTotals.Size = new Size(200, 450);
            this.pnlTotals.TabIndex = 1;

            // 
            // lblTotalsTitle
            // 
            this.lblTotalsTitle.AutoSize = true;
            this.lblTotalsTitle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.lblTotalsTitle.ForeColor = Color.FromArgb(46, 134, 171);
            this.lblTotalsTitle.Location = new Point(15, 15);
            this.lblTotalsTitle.Name = "lblTotalsTitle";
            this.lblTotalsTitle.Size = new Size(100, 23);
            this.lblTotalsTitle.TabIndex = 0;
            this.lblTotalsTitle.Text = "الإجماليات";

            // 
            // lblTotalSamir
            // 
            this.lblTotalSamir.AutoSize = true;
            this.lblTotalSamir.Font = new Font("Tahoma", 11F);
            this.lblTotalSamir.ForeColor = Color.FromArgb(40, 167, 69);
            this.lblTotalSamir.Location = new Point(15, 60);
            this.lblTotalSamir.Name = "lblTotalSamir";
            this.lblTotalSamir.Size = new Size(60, 18);
            this.lblTotalSamir.TabIndex = 1;
            this.lblTotalSamir.Text = "سمير: 0";

            // 
            // lblTotalMaher
            // 
            this.lblTotalMaher.AutoSize = true;
            this.lblTotalMaher.Font = new Font("Tahoma", 11F);
            this.lblTotalMaher.ForeColor = Color.FromArgb(23, 162, 184);
            this.lblTotalMaher.Location = new Point(15, 90);
            this.lblTotalMaher.Name = "lblTotalMaher";
            this.lblTotalMaher.Size = new Size(60, 18);
            this.lblTotalMaher.TabIndex = 2;
            this.lblTotalMaher.Text = "ماهر: 0";

            // 
            // lblTotalRaid
            // 
            this.lblTotalRaid.AutoSize = true;
            this.lblTotalRaid.Font = new Font("Tahoma", 11F);
            this.lblTotalRaid.ForeColor = Color.FromArgb(241, 143, 1);
            this.lblTotalRaid.Location = new Point(15, 120);
            this.lblTotalRaid.Name = "lblTotalRaid";
            this.lblTotalRaid.Size = new Size(55, 18);
            this.lblTotalRaid.TabIndex = 3;
            this.lblTotalRaid.Text = "رايد: 0";

            // 
            // lblTotalHaider
            // 
            this.lblTotalHaider.AutoSize = true;
            this.lblTotalHaider.Font = new Font("Tahoma", 11F);
            this.lblTotalHaider.ForeColor = Color.FromArgb(162, 59, 114);
            this.lblTotalHaider.Location = new Point(15, 150);
            this.lblTotalHaider.Name = "lblTotalHaider";
            this.lblTotalHaider.Size = new Size(60, 18);
            this.lblTotalHaider.TabIndex = 4;
            this.lblTotalHaider.Text = "حيدر: 0";

            // 
            // lblTotalLate
            // 
            this.lblTotalLate.AutoSize = true;
            this.lblTotalLate.Font = new Font("Tahoma", 11F);
            this.lblTotalLate.ForeColor = Color.FromArgb(220, 53, 69);
            this.lblTotalLate.Location = new Point(15, 180);
            this.lblTotalLate.Name = "lblTotalLate";
            this.lblTotalLate.Size = new Size(70, 18);
            this.lblTotalLate.TabIndex = 5;
            this.lblTotalLate.Text = "متأخر: 0";

            // 
            // lblTotalReceived
            // 
            this.lblTotalReceived.AutoSize = true;
            this.lblTotalReceived.Font = new Font("Tahoma", 11F);
            this.lblTotalReceived.ForeColor = Color.FromArgb(108, 117, 125);
            this.lblTotalReceived.Location = new Point(15, 210);
            this.lblTotalReceived.Name = "lblTotalReceived";
            this.lblTotalReceived.Size = new Size(65, 18);
            this.lblTotalReceived.TabIndex = 6;
            this.lblTotalReceived.Text = "واصل: 0";

            // 
            // lblGrandTotal
            // 
            this.lblGrandTotal.AutoSize = true;
            this.lblGrandTotal.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblGrandTotal.ForeColor = Color.FromArgb(46, 134, 171);
            this.lblGrandTotal.Location = new Point(15, 250);
            this.lblGrandTotal.Name = "lblGrandTotal";
            this.lblGrandTotal.Size = new Size(140, 19);
            this.lblGrandTotal.TabIndex = 7;
            this.lblGrandTotal.Text = "إجمالي الديون: 0";

            // 
            // lblGrandNet
            // 
            this.lblGrandNet.AutoSize = true;
            this.lblGrandNet.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblGrandNet.ForeColor = Color.FromArgb(220, 53, 69);
            this.lblGrandNet.Location = new Point(15, 280);
            this.lblGrandNet.Name = "lblGrandNet";
            this.lblGrandNet.Size = new Size(80, 19);
            this.lblGrandNet.TabIndex = 8;
            this.lblGrandNet.Text = "الصافي: 0";

            // 
            // pnlButtons
            // 
            this.pnlButtons.BackColor = Color.FromArgb(248, 249, 250);
            this.pnlButtons.BorderStyle = BorderStyle.FixedSingle;
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnClear);
            this.pnlButtons.Controls.Add(this.btnRefresh);
            this.pnlButtons.Controls.Add(this.btnClose);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Location = new Point(0, 600);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Padding = new Padding(10);
            this.pnlButtons.Size = new Size(1200, 60);
            this.pnlButtons.TabIndex = 3;

            // 
            // btnSave
            // 
            this.btnSave.BackColor = Color.FromArgb(40, 167, 69);
            this.btnSave.Enabled = false;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(15, 15);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(130, 30);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "💾 حفظ البيانات";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // 
            // btnClear
            // 
            this.btnClear.BackColor = Color.FromArgb(220, 53, 69);
            this.btnClear.Enabled = false;
            this.btnClear.FlatAppearance.BorderSize = 0;
            this.btnClear.FlatStyle = FlatStyle.Flat;
            this.btnClear.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnClear.ForeColor = Color.White;
            this.btnClear.Location = new Point(155, 15);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new Size(100, 30);
            this.btnClear.TabIndex = 1;
            this.btnClear.Text = "🗑️ مسح";
            this.btnClear.UseVisualStyleBackColor = false;
            this.btnClear.Click += new EventHandler(this.btnClear_Click);

            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = Color.FromArgb(23, 162, 184);
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatStyle = FlatStyle.Flat;
            this.btnRefresh.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(265, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new Size(100, 30);
            this.btnRefresh.TabIndex = 2;
            this.btnRefresh.Text = "🔄 تحديث";
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // 
            // btnClose
            // 
            this.btnClose.Anchor = AnchorStyles.Right;
            this.btnClose.BackColor = Color.FromArgb(108, 117, 125);
            this.btnClose.FlatAppearance.BorderSize = 0;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnClose.ForeColor = Color.White;
            this.btnClose.Location = new Point(1075, 15);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.TabIndex = 3;
            this.btnClose.Text = "❌ إغلاق";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            // 
            // pnlFooter
            // 
            this.pnlFooter.BackColor = Color.FromArgb(248, 249, 250);
            this.pnlFooter.BorderStyle = BorderStyle.FixedSingle;
            this.pnlFooter.Controls.Add(this.lblStatus);
            this.pnlFooter.Dock = DockStyle.Bottom;
            this.pnlFooter.Location = new Point(0, 660);
            this.pnlFooter.Name = "pnlFooter";
            this.pnlFooter.Size = new Size(1200, 30);
            this.pnlFooter.TabIndex = 4;

            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Tahoma", 10F);
            this.lblStatus.Location = new Point(10, 6);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(100, 17);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "جاهز للاستخدام";

            // 
            // WeeklyDataEntryForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 690);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlButtons);
            this.Controls.Add(this.pnlDateSelection);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlFooter);
            this.Font = new Font("Tahoma", 12F);
            this.MinimumSize = new Size(1000, 600);
            this.Name = "WeeklyDataEntryForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إدخال البيانات الأسبوعية - شركة الغشمي";
            this.FormClosing += new FormClosingEventHandler(this.WeeklyDataEntryForm_FormClosing);

            this.pnlHeader.ResumeLayout(false);
            this.pnlHeader.PerformLayout();
            this.pnlDateSelection.ResumeLayout(false);
            this.pnlDateSelection.PerformLayout();
            this.pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvWeeklyData)).EndInit();
            this.pnlTotals.ResumeLayout(false);
            this.pnlTotals.PerformLayout();
            this.pnlButtons.ResumeLayout(false);
            this.pnlFooter.ResumeLayout(false);
            this.pnlFooter.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private Panel pnlDateSelection;
        private Label lblDateFrom;
        private DateTimePicker dtpDateFrom;
        private Label lblDateTo;
        private DateTimePicker dtpDateTo;
        private Label lblWeekInfo;
        private Panel pnlMain;
        private DataGridView dgvWeeklyData;
        private Panel pnlTotals;
        private Label lblTotalsTitle;
        private Label lblTotalSamir;
        private Label lblTotalMaher;
        private Label lblTotalRaid;
        private Label lblTotalHaider;
        private Label lblTotalLate;
        private Label lblTotalReceived;
        private Label lblGrandTotal;
        private Label lblGrandNet;
        private Panel pnlButtons;
        private Button btnSave;
        private Button btnClear;
        private Button btnRefresh;
        private Button btnClose;
        private Panel pnlFooter;
        private Label lblStatus;
    }
}
