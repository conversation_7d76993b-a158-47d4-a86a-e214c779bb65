using AlGhashmiDebtManagement.Data.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace AlGhashmiDebtManagement.Forms
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtRepository _weeklyDebtRepository;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// منشئ النافذة الرئيسية
        /// </summary>
        public MainForm(IRaayahRepository raayahRepository, 
                       IWeeklyDebtRepository weeklyDebtRepository,
                       IServiceProvider serviceProvider)
        {
            _raayahRepository = raayahRepository ?? throw new ArgumentNullException(nameof(raayahRepository));
            _weeklyDebtRepository = weeklyDebtRepository ?? throw new ArgumentNullException(nameof(weeklyDebtRepository));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

            InitializeComponent();
            SetupArabicUI();
            LoadStatistics();
        }

        /// <summary>
        /// إعداد الواجهة العربية
        /// </summary>
        private void SetupArabicUI()
        {
            // إعداد اتجاه النص من اليمين إلى اليسار
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد الخط العربي
            this.Font = new Font("Tahoma", 12F, FontStyle.Regular);

            // إعداد عنوان النافذة
            this.Text = "نظام إدارة ديون الرعية - شركة الغشمي";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;

            // إعداد الألوان
            this.BackColor = Color.FromArgb(248, 249, 250);
        }

        /// <summary>
        /// تحميل الإحصائيات السريعة
        /// </summary>
        private async void LoadStatistics()
        {
            try
            {
                // تحميل عدد الرعية النشطين
                var activeRaayahCount = await _raayahRepository.GetCountAsync(false);
                
                // تحميل إحصائيات الأسبوع الحالي
                var weekStart = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                var weekEnd = weekStart.AddDays(6);
                
                var weekStats = await _weeklyDebtRepository.GetStatisticsAsync(weekStart, weekEnd);

                // تحديث التسميات (سيتم إضافتها في Designer)
                UpdateStatisticsLabels(activeRaayahCount, weekStats);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تحميل الإحصائيات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }
        }

        /// <summary>
        /// تحديث تسميات الإحصائيات
        /// </summary>
        private void UpdateStatisticsLabels(int raayahCount, (decimal TotalDebt, decimal TotalReceived, decimal NetAmount, int RecordsCount) stats)
        {
            if (lblActiveRaayahCount != null)
                lblActiveRaayahCount.Text = $"عدد الرعية النشطين: {raayahCount}";

            if (lblWeeklyDebt != null)
                lblWeeklyDebt.Text = $"إجمالي ديون الأسبوع: {stats.TotalDebt:N0} ريال";

            if (lblWeeklyReceived != null)
                lblWeeklyReceived.Text = $"إجمالي الواصل: {stats.TotalReceived:N0} ريال";

            if (lblWeeklyNet != null)
                lblWeeklyNet.Text = $"الصافي: {stats.NetAmount:N0} ريال";
        }

        /// <summary>
        /// فتح نافذة إدخال البيانات الأسبوعية
        /// </summary>
        private void btnWeeklyDataEntry_Click(object sender, EventArgs e)
        {
            try
            {
                using var weeklyForm = new WeeklyDataEntryForm(_raayahRepository, _weeklyDebtRepository);
                weeklyForm.ShowDialog();

                // تحديث الإحصائيات بعد إغلاق النافذة
                LoadStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة إدخال البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// فتح نافذة إدارة الرعية
        /// </summary>
        private void btnRaayahManagement_Click(object sender, EventArgs e)
        {
            try
            {
                using var raayahForm = new RaayahManagementForm(_raayahRepository);
                raayahForm.ShowDialog();

                // تحديث الإحصائيات بعد إغلاق النافذة
                LoadStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة إدارة الرعية:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// فتح نافذة التقارير
        /// </summary>
        private void btnReports_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء الخدمات المطلوبة
                var calculationService = new Services.DebtCalculationService();
                var reportService = new Services.ReportService(_raayahRepository, _weeklyDebtRepository, calculationService);
                var exportService = new Services.ExportService();

                using var reportsForm = new ReportsForm(reportService, calculationService, exportService);
                reportsForm.ShowDialog();

                // تحديث الإحصائيات بعد إغلاق النافذة
                LoadStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة التقارير:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// فتح نافذة الإعدادات
        /// </summary>
        private void btnSettings_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show(
                    "سيتم إضافة نافذة الإعدادات قريباً",
                    "قيد التطوير",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة الإعدادات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private void btnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show(
                    "سيتم إضافة وظيفة النسخ الاحتياطي قريباً",
                    "قيد التطوير",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في إنشاء النسخة الاحتياطية:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// عرض الإحصائيات التفصيلية
        /// </summary>
        private void btnStatistics_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show(
                    "سيتم إضافة نافذة الإحصائيات التفصيلية قريباً",
                    "قيد التطوير",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في عرض الإحصائيات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadStatistics();
        }

        /// <summary>
        /// إغلاق التطبيق
        /// </summary>
        private void btnExit_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق التطبيق؟",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                var result = MessageBox.Show(
                    "هل تريد إغلاق التطبيق؟",
                    "تأكيد الإغلاق",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnFormClosing(e);
        }
    }
}
