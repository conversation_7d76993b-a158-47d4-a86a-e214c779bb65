using AlGhashmiDebtManagement.Data.Repositories;
using AlGhashmiDebtManagement.Models;
using AlGhashmiDebtManagement.Helpers;

namespace AlGhashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة إدارة الرعية
    /// </summary>
    public partial class RaayahManagementForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private List<Raayah> _raayahList;
        private bool _isLoading = false;

        /// <summary>
        /// منشئ نافذة إدارة الرعية
        /// </summary>
        public RaayahManagementForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository ?? throw new ArgumentNullException(nameof(raayahRepository));
            _raayahList = new List<Raayah>();

            InitializeComponent();
            SetupArabicUI();
            LoadRaayahData();
        }

        /// <summary>
        /// إعداد الواجهة العربية
        /// </summary>
        private void SetupArabicUI()
        {
            // إعداد اتجاه النص من اليمين إلى اليسار
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد الخط العربي
            this.Font = new Font("Tahoma", 12F, FontStyle.Regular);

            // إعداد عنوان النافذة
            this.Text = "إدارة بيانات الرعية - شركة الغشمي";
            this.StartPosition = FormStartPosition.CenterParent;

            // إعداد الألوان
            this.BackColor = Color.FromArgb(248, 249, 250);

            // إعداد DataGridView
            SetupDataGridView();
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            dgvRaayah.AutoGenerateColumns = false;
            dgvRaayah.AllowUserToAddRows = false;
            dgvRaayah.AllowUserToDeleteRows = false;
            dgvRaayah.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvRaayah.MultiSelect = false;
            dgvRaayah.ReadOnly = false;

            // إعداد الألوان
            dgvRaayah.BackgroundColor = Color.White;
            dgvRaayah.GridColor = Color.FromArgb(224, 224, 224);
            dgvRaayah.DefaultCellStyle.SelectionBackColor = Color.FromArgb(46, 134, 171);
            dgvRaayah.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvRaayah.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(46, 134, 171);
            dgvRaayah.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvRaayah.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12F, FontStyle.Bold);

            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        /// <summary>
        /// إضافة أعمدة الجدول
        /// </summary>
        private void AddDataGridViewColumns()
        {
            // عمود المعرف (مخفي)
            dgvRaayah.Columns.Add(ColumnHelper.CreateTextColumn(
                ColumnHelper.Columns.Raayah.Id,
                "Id",
                ColumnHelper.Headers.Raayah.Id,
                visible: false
            ));

            // عمود الاسم الكامل
            dgvRaayah.Columns.Add(ColumnHelper.CreateTextColumn(
                ColumnHelper.Columns.Raayah.FullName,
                "FullName",
                ColumnHelper.Headers.Raayah.FullName,
                200,
                readOnly: false
            ));

            // عمود تفعيل خصم الحوالة
            dgvRaayah.Columns.Add(ColumnHelper.CreateCheckBoxColumn(
                ColumnHelper.Columns.Raayah.EnableDiscount,
                "EnableDiscount",
                ColumnHelper.Headers.Raayah.EnableDiscount,
                100,
                readOnly: false
            ));

            // عمود كشف الأوزري
            dgvRaayah.Columns.Add(ColumnHelper.CreateCheckBoxColumn(
                ColumnHelper.Columns.Raayah.InKashfOzri,
                "InKashfOzri",
                ColumnHelper.Headers.Raayah.InKashfOzri,
                100,
                readOnly: false
            ));

            // عمود خارج الكشف
            dgvRaayah.Columns.Add(ColumnHelper.CreateCheckBoxColumn(
                ColumnHelper.Columns.Raayah.InKharijKashf,
                "InKharijKashf",
                ColumnHelper.Headers.Raayah.InKharijKashf,
                100,
                readOnly: false
            ));

            // عمود تاريخ الإنشاء
            dgvRaayah.Columns.Add(ColumnHelper.CreateDateColumn(
                ColumnHelper.Columns.Raayah.CreatedDate,
                "CreatedDate",
                ColumnHelper.Headers.Raayah.CreatedDate,
                120,
                readOnly: true
            ));

            // عمود الحالة
            dgvRaayah.Columns.Add(ColumnHelper.CreateCheckBoxColumn(
                ColumnHelper.Columns.Raayah.IsActive,
                "IsActive",
                ColumnHelper.Headers.Raayah.IsActive,
                80,
                readOnly: false
            ));

            // عمود الملاحظات
            dgvRaayah.Columns.Add(ColumnHelper.CreateTextColumn(
                ColumnHelper.Columns.Raayah.Notes,
                "Notes",
                ColumnHelper.Headers.Raayah.Notes,
                200,
                readOnly: false
            ));

            // تطبيق التنسيق العربي
            ColumnHelper.ApplyArabicFormatting(dgvRaayah);
        }

        /// <summary>
        /// تحميل بيانات الرعية
        /// </summary>
        private async void LoadRaayahData()
        {
            try
            {
                _isLoading = true;
                lblStatus.Text = "جاري تحميل البيانات...";
                
                // تحميل جميع الرعية (بما في ذلك غير النشطين)
                var raayahData = await _raayahRepository.GetAllAsync(true);
                _raayahList = raayahData.ToList();

                // ربط البيانات بالجدول
                dgvRaayah.DataSource = _raayahList;

                // تحديث العدادات
                UpdateCounters();

                lblStatus.Text = $"تم تحميل {_raayahList.Count} رعوي";
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تحميل بيانات الرعية:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// تحديث العدادات
        /// </summary>
        private void UpdateCounters()
        {
            var activeCount = _raayahList.Count(r => r.IsActive);
            var inactiveCount = _raayahList.Count(r => !r.IsActive);
            var discountEnabledCount = _raayahList.Count(r => r.IsActive && r.EnableDiscount);

            lblActiveCount.Text = $"النشطين: {activeCount}";
            lblInactiveCount.Text = $"غير النشطين: {inactiveCount}";
            lblDiscountCount.Text = $"خصم الحوالة: {discountEnabledCount}";
        }

        /// <summary>
        /// البحث في الرعية
        /// </summary>
        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                var searchTerm = txtSearch.Text.Trim();
                
                if (string.IsNullOrEmpty(searchTerm))
                {
                    dgvRaayah.DataSource = _raayahList;
                }
                else
                {
                    var filteredList = _raayahList
                        .Where(r => r.FullName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                    
                    dgvRaayah.DataSource = filteredList;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في البحث:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }
        }

        /// <summary>
        /// إضافة رعوي جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                using var addForm = new AddEditRaayahForm(_raayahRepository);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadRaayahData();
                    lblStatus.Text = "تم إضافة رعوي جديد بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في إضافة رعوي جديد:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// تعديل رعوي محدد
        /// </summary>
        private async void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvRaayah.SelectedRows.Count == 0)
                {
                    MessageBox.Show(
                        "يرجى اختيار رعوي للتعديل",
                        "تنبيه",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                var selectedRaayah = dgvRaayah.SelectedRows[0].DataBoundItem as Raayah;
                if (selectedRaayah == null) return;

                using var editForm = new AddEditRaayahForm(_raayahRepository, selectedRaayah);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadRaayahData();
                    lblStatus.Text = "تم تحديث بيانات الرعوي بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تعديل الرعوي:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// حذف رعوي محدد
        /// </summary>
        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvRaayah.SelectedRows.Count == 0)
                {
                    MessageBox.Show(
                        "يرجى اختيار رعوي للحذف",
                        "تنبيه",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                var selectedRaayah = dgvRaayah.SelectedRows[0].DataBoundItem as Raayah;
                if (selectedRaayah == null) return;

                var result = MessageBox.Show(
                    $"هل تريد حذف الرعوي '{selectedRaayah.FullName}'؟\n\nملاحظة: سيتم تعطيل الرعوي وليس حذفه نهائياً",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result == DialogResult.Yes)
                {
                    await _raayahRepository.SoftDeleteAsync(selectedRaayah.Id);
                    LoadRaayahData();
                    lblStatus.Text = "تم تعطيل الرعوي بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في حذف الرعوي:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// حفظ التغييرات المباشرة في الجدول
        /// </summary>
        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "جاري حفظ التغييرات...";

                foreach (DataGridViewRow row in dgvRaayah.Rows)
                {
                    if (row.DataBoundItem is Raayah raayah)
                    {
                        await _raayahRepository.UpdateAsync(raayah);
                    }
                }

                UpdateCounters();
                lblStatus.Text = "تم حفظ جميع التغييرات بنجاح";

                MessageBox.Show(
                    "تم حفظ جميع التغييرات بنجاح",
                    "نجح الحفظ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في حفظ التغييرات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
                lblStatus.Text = "خطأ في حفظ التغييرات";
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadRaayahData();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// معالج تغيير الخلية
        /// </summary>
        private void dgvRaayah_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (_isLoading || e.RowIndex < 0) return;

            // تمييز الصف المعدل
            dgvRaayah.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
            
            UpdateCounters();
        }

        /// <summary>
        /// معالج النقر المزدوج على الصف
        /// </summary>
        private void dgvRaayah_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }
    }
}
