using AlGhashmiDebtManagement.Data;
using AlGhashmiDebtManagement.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using AlGhashmiDebtManagement.Data.Repositories;

namespace AlGhashmiDebtManagement;

static class Program
{
    /// <summary>
    /// نقطة الدخول الرئيسية للتطبيق
    /// </summary>
    [STAThread]
    static void Main(string[] args)
    {
        // التحقق من وسائط سطر الأوامر للتشخيص
        if (args.Length > 0 && args[0] == "diagnose")
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            DatabaseDiagnostic.DiagnoseDatabase();
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
            return;
        }

        // إعداد التطبيق
        ApplicationConfiguration.Initialize();

        try
        {
            // إعداد خدمات التطبيق
            var services = ConfigureServices();
            var serviceProvider = services.BuildServiceProvider();

            // تهيئة قاعدة البيانات
            InitializeDatabase(serviceProvider);

            // تشغيل النافذة الرئيسية
            var mainForm = serviceProvider.GetRequiredService<MainForm>();
            Application.Run(mainForm);
        }
        catch (Exception ex)
        {
            // عرض رسالة خطأ في حالة فشل التطبيق
            var errorMessage = $"حدث خطأ في تشغيل التطبيق:\n{ex.Message}\n\nتفاصيل الخطأ:\n{ex}";

            MessageBox.Show(
                errorMessage,
                "خطأ في التطبيق",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );

            // كتابة الخطأ في ملف لوج
            try
            {
                var logPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "AlGhashmiDebtManagement",
                    "error.log"
                );
                Directory.CreateDirectory(Path.GetDirectoryName(logPath)!);
                File.AppendAllText(logPath, $"{DateTime.Now}: {errorMessage}\n\n");
            }
            catch { /* تجاهل أخطاء كتابة اللوج */ }
        }
    }

    /// <summary>
    /// إعداد خدمات التطبيق
    /// </summary>
    /// <returns>مجموعة الخدمات</returns>
    private static IServiceCollection ConfigureServices()
    {
        var services = new ServiceCollection();

        // إضافة DbContext
        services.AddDbContext<AppDbContext>(options =>
        {
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "AlGhashmiDebtManagement",
                "AlGhashmiDebtManagement.db"
            );

            Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);
            options.UseSqlite($"Data Source={dbPath}");
        });

        // إضافة المستودعات
        services.AddScoped<IRaayahRepository, RaayahRepository>();
        services.AddScoped<IWeeklyDebtRepository, WeeklyDebtRepository>();

        // إضافة الخدمات
        services.AddScoped<AlGhashmiDebtManagement.Services.DebtCalculationService>();
        services.AddScoped<AlGhashmiDebtManagement.Services.ReportService>();
        services.AddScoped<AlGhashmiDebtManagement.Services.ExportService>();

        // إضافة النوافذ
        services.AddTransient<MainForm>();
        services.AddTransient<RaayahManagementForm>();
        services.AddTransient<AddEditRaayahForm>();
        services.AddTransient<WeeklyDataEntryForm>();
        services.AddTransient<ReportsForm>();
        // سيتم إضافة باقي النوافذ لاحقاً

        return services;
    }

    /// <summary>
    /// تهيئة قاعدة البيانات
    /// </summary>
    /// <param name="serviceProvider">مزود الخدمات</param>
    private static void InitializeDatabase(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        try
        {
            // تهيئة قاعدة البيانات
            DatabaseInitializer.Initialize(context);
        }
        catch (Exception ex)
        {
            var errorMessage = $"خطأ في تهيئة قاعدة البيانات:\n{ex.Message}\n\nهل تريد إعادة إنشاء قاعدة البيانات؟";

            var result = MessageBox.Show(
                errorMessage,
                "خطأ في قاعدة البيانات",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning
            );

            if (result == DialogResult.Yes)
            {
                try
                {
                    // محاولة إعادة إنشاء قاعدة البيانات
                    context.Database.EnsureDeleted();
                    context.Database.EnsureCreated();
                    DatabaseInitializer.Initialize(context);

                    MessageBox.Show(
                        "تم إعادة إنشاء قاعدة البيانات بنجاح",
                        "نجح الإصلاح",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                catch (Exception recreateEx)
                {
                    MessageBox.Show(
                        $"فشل في إعادة إنشاء قاعدة البيانات:\n{recreateEx.Message}",
                        "خطأ حرج",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error
                    );
                    throw;
                }
            }
            else
            {
                throw;
            }
        }
    }
}