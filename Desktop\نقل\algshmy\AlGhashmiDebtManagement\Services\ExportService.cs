using ClosedXML.Excel;
using AlGhashmiDebtManagement.Models;
using AlGhashmiDebtManagement.Helpers;
using System.Text;

namespace AlGhashmiDebtManagement.Services
{
    /// <summary>
    /// خدمة تصدير التقارير إلى Excel
    /// </summary>
    public class ExportService
    {
        /// <summary>
        /// تصدير التقرير إلى Excel
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="summary">ملخص التقرير</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>true إذا تم التصدير بنجاح</returns>
        public async Task<bool> ExportToExcelAsync(IEnumerable<ReportData> reportData, ReportSummary summary, string filePath)
        {
            try
            {
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("تقرير الديون");

                // إعداد اتجاه الورقة من اليمين إلى اليسار
                worksheet.RightToLeft = true;

                // إضافة عنوان التقرير
                var titleCell = worksheet.Cell(1, 1);
                titleCell.Value = "تقرير ديون الرعية - شركة الغشمي";
                titleCell.Style.Font.Bold = true;
                titleCell.Style.Font.FontSize = 16;
                titleCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                worksheet.Range(1, 1, 1, 10).Merge();

                // إضافة فترة التقرير
                var periodCell = worksheet.Cell(2, 1);
                periodCell.Value = $"الفترة: {summary.ReportPeriod}";
                periodCell.Style.Font.Bold = true;
                periodCell.Style.Font.FontSize = 12;
                worksheet.Range(2, 1, 2, 10).Merge();

                // إضافة تاريخ التوليد
                var dateCell = worksheet.Cell(3, 1);
                dateCell.Value = $"تاريخ التوليد: {summary.GeneratedDate:dd/MM/yyyy HH:mm}";
                dateCell.Style.Font.FontSize = 10;
                worksheet.Range(3, 1, 3, 10).Merge();

                // إضافة رؤوس الأعمدة
                var headerRow = 5;
                var headers = new[]
                {
                    ColumnHelper.Headers.Report.RaayahName,
                    ColumnHelper.Headers.Report.SamirAmount,
                    ColumnHelper.Headers.Report.MaherAmount,
                    ColumnHelper.Headers.Report.RaidAmount,
                    ColumnHelper.Headers.Report.HaiderAmount,
                    ColumnHelper.Headers.Report.LateAmount,
                    ColumnHelper.Headers.Report.ReceivedAmount,
                    ColumnHelper.Headers.Report.TotalDebtAmount,
                    ColumnHelper.Headers.Report.NetAmount
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    var headerCell = worksheet.Cell(headerRow, i + 1);
                    headerCell.Value = headers[i];
                    headerCell.Style.Font.Bold = true;
                    headerCell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                    headerCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    headerCell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                }

                // إضافة البيانات
                var currentRow = headerRow + 1;
                foreach (var data in reportData)
                {
                    worksheet.Cell(currentRow, 1).Value = data.RaayahName;
                    worksheet.Cell(currentRow, 2).Value = data.SamirAmount;
                    worksheet.Cell(currentRow, 3).Value = data.MaherAmount;
                    worksheet.Cell(currentRow, 4).Value = data.RaidAmount;
                    worksheet.Cell(currentRow, 5).Value = data.HaiderAmount;
                    worksheet.Cell(currentRow, 6).Value = data.LateAmount;
                    worksheet.Cell(currentRow, 7).Value = data.ReceivedAmount;
                    worksheet.Cell(currentRow, 8).Value = data.TotalDebtAmount;
                    worksheet.Cell(currentRow, 9).Value = data.NetAmount;

                    // تنسيق الأرقام
                    for (int col = 2; col <= 9; col++)
                    {
                        worksheet.Cell(currentRow, col).Style.NumberFormat.Format = "#,##0";
                        worksheet.Cell(currentRow, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                        worksheet.Cell(currentRow, col).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    }

                    worksheet.Cell(currentRow, 1).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    currentRow++;
                }

                // إضافة صف الإجماليات
                var totalRow = currentRow + 1;
                worksheet.Cell(totalRow, 1).Value = "الإجماليات";
                worksheet.Cell(totalRow, 1).Style.Font.Bold = true;
                worksheet.Cell(totalRow, 2).Value = summary.TotalSamirAmount;
                worksheet.Cell(totalRow, 3).Value = summary.TotalMaherAmount;
                worksheet.Cell(totalRow, 4).Value = summary.TotalRaidAmount;
                worksheet.Cell(totalRow, 5).Value = summary.TotalHaiderAmount;
                worksheet.Cell(totalRow, 6).Value = summary.TotalLateAmount;
                worksheet.Cell(totalRow, 7).Value = summary.TotalReceivedAmount;
                worksheet.Cell(totalRow, 8).Value = summary.GrandTotalDebtAmount;
                worksheet.Cell(totalRow, 9).Value = summary.GrandNetAmount;

                // تنسيق صف الإجماليات
                for (int col = 1; col <= 9; col++)
                {
                    var cell = worksheet.Cell(totalRow, col);
                    cell.Style.Font.Bold = true;
                    cell.Style.Fill.BackgroundColor = XLColor.LightGray;
                    cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                    if (col > 1)
                    {
                        cell.Style.NumberFormat.Format = "#,##0";
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }
                }

                // تعديل عرض الأعمدة
                worksheet.Column(1).Width = 25; // اسم الرعوي
                for (int col = 2; col <= 9; col++)
                {
                    worksheet.Column(col).Width = 15;
                }

                // حفظ الملف
                await Task.Run(() => workbook.SaveAs(filePath));
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصدير Excel: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تصدير التقرير إلى PDF (غير مدعوم حالياً)
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="summary">ملخص التقرير</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>true إذا تم التصدير بنجاح</returns>
        public async Task<bool> ExportToPdfAsync(IEnumerable<ReportData> reportData, ReportSummary summary, string filePath)
        {
            try
            {
                // PDF غير مدعوم حالياً - سيتم إضافته لاحقاً
                await Task.Delay(100); // محاكاة عملية التصدير
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصدير PDF: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على مسار افتراضي للتصدير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="fileExtension">امتداد الملف</param>
        /// <returns>مسار الملف</returns>
        public string GetDefaultExportPath(ReportType reportType, string fileExtension)
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var exportDir = Path.Combine(documentsPath, "AlGhashmiDebtManagement", "Reports");
            
            Directory.CreateDirectory(exportDir);

            var reportTypeName = reportType switch
            {
                ReportType.FullReport => "التقرير_الكامل",
                ReportType.ReportWithDiscount => "تقرير_خصم_الحوالة",
                ReportType.CardsReport => "كشف_البطاقات",
                ReportType.KashfOzri => "كشف_الأوزري",
                ReportType.KharijKashf => "خارج_الكشف",
                _ => "تقرير"
            };

            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = $"{reportTypeName}_{timestamp}.{fileExtension}";
            
            return Path.Combine(exportDir, fileName);
        }

        /// <summary>
        /// تصدير كشف البطاقات (غير مدعوم حالياً)
        /// </summary>
        /// <param name="cardsData">بيانات البطاقات</param>
        /// <param name="exportDirectory">مجلد التصدير</param>
        /// <returns>عدد البطاقات المصدرة</returns>
        public async Task<int> ExportCardsReportAsync(IEnumerable<CardReportData> cardsData, string exportDirectory)
        {
            try
            {
                // كشف البطاقات غير مدعوم حالياً - سيتم إضافته لاحقاً
                await Task.Delay(100);
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصدير كشف البطاقات: {ex.Message}");
                return 0;
            }
        }
    }
}
