using System.ComponentModel.DataAnnotations.Schema;

namespace AlGhashmiDebtManagement.Models
{
    /// <summary>
    /// أنواع التقارير المتاحة
    /// </summary>
    public enum ReportType
    {
        /// <summary>
        /// التقرير الكامل
        /// </summary>
        FullReport = 1,

        /// <summary>
        /// التقرير مع خصم الحوالة
        /// </summary>
        ReportWithDiscount = 2,

        /// <summary>
        /// كشف بطاقات
        /// </summary>
        CardsReport = 3,

        /// <summary>
        /// كشف الأوزري
        /// </summary>
        KashfOzri = 4,

        /// <summary>
        /// خارج الكشف
        /// </summary>
        KharijKashf = 5
    }

    /// <summary>
    /// نموذج بيانات التقرير الأساسي
    /// </summary>
    public class ReportData
    {
        /// <summary>
        /// معرف الرعوي
        /// </summary>
        public int RaayahId { get; set; }

        /// <summary>
        /// اسم الرعوي
        /// </summary>
        public string RaayahName { get; set; } = string.Empty;

        /// <summary>
        /// مبلغ فرع سمير
        /// </summary>
        public decimal SamirAmount { get; set; }

        /// <summary>
        /// مبلغ فرع ماهر
        /// </summary>
        public decimal MaherAmount { get; set; }

        /// <summary>
        /// مبلغ فرع رايد
        /// </summary>
        public decimal RaidAmount { get; set; }

        /// <summary>
        /// مبلغ فرع حيدر
        /// </summary>
        public decimal HaiderAmount { get; set; }

        /// <summary>
        /// المبلغ المتأخر
        /// </summary>
        public decimal LateAmount { get; set; }

        /// <summary>
        /// المبلغ الواصل
        /// </summary>
        public decimal ReceivedAmount { get; set; }

        /// <summary>
        /// إجمالي ديون الفروع
        /// </summary>
        public decimal TotalBranchesAmount => SamirAmount + MaherAmount + RaidAmount + HaiderAmount;

        /// <summary>
        /// إجمالي الديون
        /// </summary>
        public decimal TotalDebtAmount => TotalBranchesAmount + LateAmount;

        /// <summary>
        /// الصافي
        /// </summary>
        public decimal NetAmount => TotalDebtAmount - ReceivedAmount;

        /// <summary>
        /// خصم الحوالة (3%)
        /// </summary>
        public decimal DiscountAmount => NetAmount * 0.03m;

        /// <summary>
        /// الصافي بعد الخصم
        /// </summary>
        public decimal NetAfterDiscount => NetAmount - DiscountAmount;

        /// <summary>
        /// تفعيل خصم الحوالة
        /// </summary>
        public bool EnableDiscount { get; set; }

        /// <summary>
        /// في كشف الأوزري
        /// </summary>
        public bool InKashfOzri { get; set; }

        /// <summary>
        /// في خارج الكشف
        /// </summary>
        public bool InKharijKashf { get; set; }
    }

    /// <summary>
    /// نموذج تقرير البطاقة الفردية
    /// </summary>
    public class CardReportData : ReportData
    {
        /// <summary>
        /// فترة التقرير
        /// </summary>
        public string ReportPeriod { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime ReportDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج ملخص التقرير
    /// </summary>
    public class ReportSummary
    {
        /// <summary>
        /// عدد الرعية
        /// </summary>
        public int TotalRaayahCount { get; set; }

        /// <summary>
        /// إجمالي مبلغ سمير
        /// </summary>
        public decimal TotalSamirAmount { get; set; }

        /// <summary>
        /// إجمالي مبلغ ماهر
        /// </summary>
        public decimal TotalMaherAmount { get; set; }

        /// <summary>
        /// إجمالي مبلغ رايد
        /// </summary>
        public decimal TotalRaidAmount { get; set; }

        /// <summary>
        /// إجمالي مبلغ حيدر
        /// </summary>
        public decimal TotalHaiderAmount { get; set; }

        /// <summary>
        /// إجمالي المبلغ المتأخر
        /// </summary>
        public decimal TotalLateAmount { get; set; }

        /// <summary>
        /// إجمالي المبلغ الواصل
        /// </summary>
        public decimal TotalReceivedAmount { get; set; }

        /// <summary>
        /// إجمالي ديون الفروع
        /// </summary>
        public decimal TotalBranchesAmount => TotalSamirAmount + TotalMaherAmount + TotalRaidAmount + TotalHaiderAmount;

        /// <summary>
        /// إجمالي الديون
        /// </summary>
        public decimal GrandTotalDebtAmount => TotalBranchesAmount + TotalLateAmount;

        /// <summary>
        /// إجمالي الصافي
        /// </summary>
        public decimal GrandNetAmount => GrandTotalDebtAmount - TotalReceivedAmount;

        /// <summary>
        /// إجمالي خصم الحوالة
        /// </summary>
        public decimal TotalDiscountAmount { get; set; }

        /// <summary>
        /// إجمالي الصافي بعد الخصم
        /// </summary>
        public decimal GrandNetAfterDiscount => GrandNetAmount - TotalDiscountAmount;

        /// <summary>
        /// فترة التقرير
        /// </summary>
        public string ReportPeriod { get; set; } = string.Empty;

        /// <summary>
        /// نوع التقرير
        /// </summary>
        public ReportType ReportType { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج معايير التقرير
    /// </summary>
    public class ReportCriteria
    {
        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime DateFrom { get; set; }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime DateTo { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        public ReportType ReportType { get; set; }

        /// <summary>
        /// تضمين الرعية غير النشطين
        /// </summary>
        public bool IncludeInactive { get; set; } = false;

        /// <summary>
        /// فترة التقرير (للعرض)
        /// </summary>
        public string ReportPeriod => $"{DateFrom:dd/MM/yyyy} - {DateTo:dd/MM/yyyy}";

        /// <summary>
        /// التحقق من صحة المعايير
        /// </summary>
        public bool IsValid()
        {
            return DateTo >= DateFrom && (DateTo - DateFrom).Days <= 7;
        }
    }
}
