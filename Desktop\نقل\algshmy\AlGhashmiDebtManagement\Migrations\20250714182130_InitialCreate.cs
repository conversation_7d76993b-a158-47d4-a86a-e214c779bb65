﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AlGhashmiDebtManagement.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "<PERSON><PERSON><PERSON>",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FullName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    EnableDiscount = table.Column<bool>(type: "INTEGER", nullable: false),
                    InKashfOzri = table.Column<bool>(type: "INTEGER", nullable: false),
                    InKharijKashf = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    LastModifiedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Raayah", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WeeklyDebts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RaayahId = table.Column<int>(type: "INTEGER", nullable: false),
                    DateFrom = table.Column<DateTime>(type: "TEXT", nullable: false),
                    DateTo = table.Column<DateTime>(type: "TEXT", nullable: false),
                    SamirAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    MaherAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    RaidAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    HaiderAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    LateAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    ReceivedAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, defaultValue: 0m),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    LastModifiedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WeeklyDebts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WeeklyDebts_Raayah_RaayahId",
                        column: x => x.RaayahId,
                        principalTable: "Raayah",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Raayah_FullName",
                table: "Raayah",
                column: "FullName");

            migrationBuilder.CreateIndex(
                name: "IX_Raayah_IsActive",
                table: "Raayah",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_WeeklyDebts_DateRange",
                table: "WeeklyDebts",
                columns: new[] { "DateFrom", "DateTo" });

            migrationBuilder.CreateIndex(
                name: "IX_WeeklyDebts_RaayahId",
                table: "WeeklyDebts",
                column: "RaayahId");

            migrationBuilder.CreateIndex(
                name: "IX_WeeklyDebts_Unique",
                table: "WeeklyDebts",
                columns: new[] { "RaayahId", "DateFrom", "DateTo" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WeeklyDebts");

            migrationBuilder.DropTable(
                name: "Raayah");
        }
    }
}
