using AlGhashmiDebtManagement.Data.Repositories;
using AlGhashmiDebtManagement.Models;
using AlGhashmiDebtManagement.Helpers;

namespace AlGhashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة إدخال البيانات الأسبوعية
    /// </summary>
    public partial class WeeklyDataEntryForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtRepository _weeklyDebtRepository;
        private List<Raayah> _activeRaayahList;
        private List<WeeklyDebtEntry> _weeklyEntries;
        private bool _isLoading = false;

        /// <summary>
        /// منشئ نافذة إدخال البيانات الأسبوعية
        /// </summary>
        public WeeklyDataEntryForm(IRaayahRepository raayahRepository, IWeeklyDebtRepository weeklyDebtRepository)
        {
            _raayahRepository = raayahRepository ?? throw new ArgumentNullException(nameof(raayahRepository));
            _weeklyDebtRepository = weeklyDebtRepository ?? throw new ArgumentNullException(nameof(weeklyDebtRepository));
            _activeRaayahList = new List<Raayah>();
            _weeklyEntries = new List<WeeklyDebtEntry>();

            InitializeComponent();
            SetupArabicUI();
            SetupDefaultDates();
            LoadActiveRaayah();
        }

        /// <summary>
        /// إعداد الواجهة العربية
        /// </summary>
        private void SetupArabicUI()
        {
            // إعداد اتجاه النص من اليمين إلى اليسار
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد الخط العربي
            this.Font = new Font("Tahoma", 12F, FontStyle.Regular);

            // إعداد عنوان النافذة
            this.Text = "إدخال البيانات الأسبوعية - شركة الغشمي";
            this.StartPosition = FormStartPosition.CenterParent;

            // إعداد الألوان
            this.BackColor = Color.FromArgb(248, 249, 250);

            // إعداد DataGridView
            SetupDataGridView();
        }

        /// <summary>
        /// إعداد التواريخ الافتراضية
        /// </summary>
        private void SetupDefaultDates()
        {
            // تعيين بداية الأسبوع الحالي (الأحد)
            var today = DateTime.Today;
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            dtpDateFrom.Value = startOfWeek;
            dtpDateTo.Value = endOfWeek;

            // ربط أحداث تغيير التاريخ
            dtpDateFrom.ValueChanged += DatePicker_ValueChanged;
            dtpDateTo.ValueChanged += DatePicker_ValueChanged;
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            dgvWeeklyData.AutoGenerateColumns = false;
            dgvWeeklyData.AllowUserToAddRows = false;
            dgvWeeklyData.AllowUserToDeleteRows = false;
            dgvWeeklyData.SelectionMode = DataGridViewSelectionMode.CellSelect;
            dgvWeeklyData.MultiSelect = false;
            dgvWeeklyData.ReadOnly = false;

            // إعداد الألوان
            dgvWeeklyData.BackgroundColor = Color.White;
            dgvWeeklyData.GridColor = Color.FromArgb(224, 224, 224);
            dgvWeeklyData.DefaultCellStyle.SelectionBackColor = Color.FromArgb(46, 134, 171);
            dgvWeeklyData.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvWeeklyData.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(46, 134, 171);
            dgvWeeklyData.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvWeeklyData.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            dgvWeeklyData.RowHeadersWidth = 30;

            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        /// <summary>
        /// إضافة أعمدة الجدول
        /// </summary>
        private void AddDataGridViewColumns()
        {
            // عمود معرف الرعوي (مخفي)
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateTextColumn(
                ColumnHelper.Columns.WeeklyDebt.RaayahId,
                "RaayahId",
                ColumnHelper.Headers.WeeklyDebt.RaayahId,
                visible: false
            ));

            // عمود اسم الرعوي (للقراءة فقط)
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateTextColumn(
                ColumnHelper.Columns.WeeklyDebt.RaayahName,
                "RaayahName",
                ColumnHelper.Headers.WeeklyDebt.RaayahName,
                150,
                readOnly: true
            ));

            // أعمدة الفروع الأربعة
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.SamirAmount,
                "SamirAmount",
                ColumnHelper.Headers.WeeklyDebt.SamirAmount,
                100,
                readOnly: false,
                headerColor: Color.FromArgb(40, 167, 69)
            ));

            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.MaherAmount,
                "MaherAmount",
                ColumnHelper.Headers.WeeklyDebt.MaherAmount,
                100,
                readOnly: false,
                headerColor: Color.FromArgb(23, 162, 184)
            ));

            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.RaidAmount,
                "RaidAmount",
                ColumnHelper.Headers.WeeklyDebt.RaidAmount,
                100,
                readOnly: false,
                headerColor: Color.FromArgb(241, 143, 1)
            ));

            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.HaiderAmount,
                "HaiderAmount",
                ColumnHelper.Headers.WeeklyDebt.HaiderAmount,
                100,
                readOnly: false,
                headerColor: Color.FromArgb(162, 59, 114)
            ));

            // عمود المتأخر
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.LateAmount,
                "LateAmount",
                ColumnHelper.Headers.WeeklyDebt.LateAmount,
                100,
                readOnly: false,
                headerColor: Color.FromArgb(220, 53, 69)
            ));

            // عمود الواصل
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.ReceivedAmount,
                "ReceivedAmount",
                ColumnHelper.Headers.WeeklyDebt.ReceivedAmount,
                100,
                readOnly: false,
                headerColor: Color.FromArgb(108, 117, 125)
            ));

            // عمود إجمالي الديون (محسوب تلقائياً)
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.TotalDebt,
                "TotalDebt",
                ColumnHelper.Headers.WeeklyDebt.TotalDebt,
                120,
                readOnly: true,
                backgroundColor: Color.FromArgb(255, 248, 220)
            ));

            // عمود الصافي (محسوب تلقائياً)
            dgvWeeklyData.Columns.Add(ColumnHelper.CreateNumericColumn(
                ColumnHelper.Columns.WeeklyDebt.NetAmount,
                "NetAmount",
                ColumnHelper.Headers.WeeklyDebt.NetAmount,
                120,
                readOnly: true,
                backgroundColor: Color.FromArgb(220, 248, 255)
            ));

            // تطبيق التنسيق العربي
            ColumnHelper.ApplyArabicFormatting(dgvWeeklyData);
        }

        /// <summary>
        /// إنشاء عمود رقمي
        /// </summary>
        private DataGridViewTextBoxColumn CreateNumericColumn(string name, string headerText, Color headerColor)
        {
            return new DataGridViewTextBoxColumn
            {
                Name = name,
                DataPropertyName = name,
                HeaderText = headerText,
                Width = 100,
                ReadOnly = false,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "N0",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                HeaderCell = new DataGridViewColumnHeaderCell
                {
                    Style = new DataGridViewCellStyle
                    {
                        BackColor = headerColor,
                        ForeColor = Color.White,
                        Font = new Font("Tahoma", 12F, FontStyle.Bold),
                        Alignment = DataGridViewContentAlignment.MiddleCenter
                    }
                }
            };
        }

        /// <summary>
        /// تحميل الرعية النشطين
        /// </summary>
        private async void LoadActiveRaayah()
        {
            try
            {
                _isLoading = true;
                lblStatus.Text = "جاري تحميل بيانات الرعية...";

                var raayahData = await _raayahRepository.GetActiveAsync();
                _activeRaayahList = raayahData.ToList();

                await LoadWeeklyData();

                lblStatus.Text = $"تم تحميل {_activeRaayahList.Count} رعوي نشط";
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تحميل بيانات الرعية:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// تحميل البيانات الأسبوعية
        /// </summary>
        private async Task LoadWeeklyData()
        {
            try
            {
                var dateFrom = dtpDateFrom.Value.Date;
                var dateTo = dtpDateTo.Value.Date;

                // التحقق من صحة الفترة الزمنية
                if (!IsValidDateRange(dateFrom, dateTo))
                {
                    return;
                }

                // تحميل البيانات الموجودة للفترة المحددة
                var existingDebts = await _weeklyDebtRepository.GetByWeekAsync(dateFrom, dateTo);
                var existingDebtsDict = existingDebts.ToDictionary(d => d.RaayahId);

                // إنشاء قائمة الإدخالات
                _weeklyEntries.Clear();
                foreach (var raayah in _activeRaayahList)
                {
                    var entry = new WeeklyDebtEntry
                    {
                        RaayahId = raayah.Id,
                        RaayahName = raayah.FullName,
                        DateFrom = dateFrom,
                        DateTo = dateTo
                    };

                    // إذا كانت هناك بيانات موجودة، تحميلها
                    if (existingDebtsDict.TryGetValue(raayah.Id, out var existingDebt))
                    {
                        entry.SamirAmount = existingDebt.SamirAmount;
                        entry.MaherAmount = existingDebt.MaherAmount;
                        entry.RaidAmount = existingDebt.RaidAmount;
                        entry.HaiderAmount = existingDebt.HaiderAmount;
                        entry.LateAmount = existingDebt.LateAmount;
                        entry.ReceivedAmount = existingDebt.ReceivedAmount;
                        entry.ExistingId = existingDebt.Id;
                    }

                    _weeklyEntries.Add(entry);
                }

                // ربط البيانات بالجدول
                dgvWeeklyData.DataSource = _weeklyEntries;

                // تحديث الإجماليات
                UpdateTotals();

                // تحديث حالة الأزرار
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تحميل البيانات الأسبوعية:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// التحقق من صحة الفترة الزمنية
        /// </summary>
        private bool IsValidDateRange(DateTime dateFrom, DateTime dateTo)
        {
            if (dateTo < dateFrom)
            {
                MessageBox.Show(
                    "تاريخ النهاية يجب أن يكون بعد تاريخ البداية",
                    "خطأ في التاريخ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                return false;
            }

            var daysDiff = (dateTo - dateFrom).Days;
            if (daysDiff != 6)
            {
                MessageBox.Show(
                    "يجب أن تكون الفترة 7 أيام بالضبط (من الأحد إلى السبت)",
                    "خطأ في الفترة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                return false;
            }

            return true;
        }

        /// <summary>
        /// معالج تغيير التاريخ
        /// </summary>
        private async void DatePicker_ValueChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            // تصحيح التواريخ تلقائياً لتكون أسبوع كامل
            if (sender == dtpDateFrom)
            {
                dtpDateTo.Value = dtpDateFrom.Value.AddDays(6);
            }
            else if (sender == dtpDateTo)
            {
                dtpDateFrom.Value = dtpDateTo.Value.AddDays(-6);
            }

            await LoadWeeklyData();
        }

        /// <summary>
        /// تحديث الإجماليات
        /// </summary>
        private void UpdateTotals()
        {
            if (_weeklyEntries == null || !_weeklyEntries.Any()) return;

            var totalSamir = _weeklyEntries.Sum(e => e.SamirAmount);
            var totalMaher = _weeklyEntries.Sum(e => e.MaherAmount);
            var totalRaid = _weeklyEntries.Sum(e => e.RaidAmount);
            var totalHaider = _weeklyEntries.Sum(e => e.HaiderAmount);
            var totalLate = _weeklyEntries.Sum(e => e.LateAmount);
            var totalReceived = _weeklyEntries.Sum(e => e.ReceivedAmount);
            var grandTotalDebt = _weeklyEntries.Sum(e => e.TotalDebt);
            var grandNetAmount = _weeklyEntries.Sum(e => e.NetAmount);

            // تحديث التسميات
            lblTotalSamir.Text = $"سمير: {totalSamir:N0}";
            lblTotalMaher.Text = $"ماهر: {totalMaher:N0}";
            lblTotalRaid.Text = $"رايد: {totalRaid:N0}";
            lblTotalHaider.Text = $"حيدر: {totalHaider:N0}";
            lblTotalLate.Text = $"متأخر: {totalLate:N0}";
            lblTotalReceived.Text = $"واصل: {totalReceived:N0}";
            lblGrandTotal.Text = $"إجمالي الديون: {grandTotalDebt:N0}";
            lblGrandNet.Text = $"الصافي: {grandNetAmount:N0}";
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasData = _weeklyEntries?.Any(e => e.HasData) ?? false;
            btnSave.Enabled = hasData;
            btnClear.Enabled = hasData;
        }

        /// <summary>
        /// معالج تغيير قيمة الخلية
        /// </summary>
        private void dgvWeeklyData_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (_isLoading || e.RowIndex < 0) return;

            try
            {
                // التحقق من أن القيمة رقمية
                var cell = dgvWeeklyData.Rows[e.RowIndex].Cells[e.ColumnIndex];
                if (cell.Value != null && !string.IsNullOrEmpty(cell.Value.ToString()))
                {
                    if (!decimal.TryParse(cell.Value.ToString(), out decimal value) || value < 0)
                    {
                        MessageBox.Show(
                            "يرجى إدخال رقم صحيح أكبر من أو يساوي صفر",
                            "خطأ في البيانات",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning
                        );
                        cell.Value = 0;
                        return;
                    }
                }

                // تحديث الحسابات التلقائية
                var entry = _weeklyEntries[e.RowIndex];
                entry.RecalculateAmounts();

                // تحديث الإجماليات
                UpdateTotals();
                UpdateButtonStates();

                // تمييز الصف المعدل
                dgvWeeklyData.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في معالجة البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// معالج بداية تحرير الخلية
        /// </summary>
        private void dgvWeeklyData_CellBeginEdit(object sender, DataGridViewCellCancelEventArgs e)
        {
            // السماح بالتحرير فقط في أعمدة المبالغ
            var columnName = dgvWeeklyData.Columns[e.ColumnIndex].Name;
            var editableColumns = new[] {
                ColumnHelper.Columns.WeeklyDebt.SamirAmount,
                ColumnHelper.Columns.WeeklyDebt.MaherAmount,
                ColumnHelper.Columns.WeeklyDebt.RaidAmount,
                ColumnHelper.Columns.WeeklyDebt.HaiderAmount,
                ColumnHelper.Columns.WeeklyDebt.LateAmount,
                ColumnHelper.Columns.WeeklyDebt.ReceivedAmount
            };

            if (!editableColumns.Contains(columnName))
            {
                e.Cancel = true;
            }
        }

        /// <summary>
        /// حفظ البيانات الأسبوعية
        /// </summary>
        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";
                lblStatus.Text = "جاري حفظ البيانات الأسبوعية...";

                var dateFrom = dtpDateFrom.Value.Date;
                var dateTo = dtpDateTo.Value.Date;

                // التحقق من صحة البيانات
                if (!ValidateData())
                {
                    return;
                }

                var savedCount = 0;
                var updatedCount = 0;

                foreach (var entry in _weeklyEntries.Where(e => e.HasData))
                {
                    if (entry.ExistingId.HasValue)
                    {
                        // تحديث السجل الموجود
                        var existingDebt = await _weeklyDebtRepository.GetByIdAsync(entry.ExistingId.Value);
                        if (existingDebt != null)
                        {
                            existingDebt.SamirAmount = entry.SamirAmount;
                            existingDebt.MaherAmount = entry.MaherAmount;
                            existingDebt.RaidAmount = entry.RaidAmount;
                            existingDebt.HaiderAmount = entry.HaiderAmount;
                            existingDebt.LateAmount = entry.LateAmount;
                            existingDebt.ReceivedAmount = entry.ReceivedAmount;

                            await _weeklyDebtRepository.UpdateAsync(existingDebt);
                            updatedCount++;
                        }
                    }
                    else
                    {
                        // إضافة سجل جديد
                        var newDebt = new WeeklyDebt
                        {
                            RaayahId = entry.RaayahId,
                            DateFrom = dateFrom,
                            DateTo = dateTo,
                            SamirAmount = entry.SamirAmount,
                            MaherAmount = entry.MaherAmount,
                            RaidAmount = entry.RaidAmount,
                            HaiderAmount = entry.HaiderAmount,
                            LateAmount = entry.LateAmount,
                            ReceivedAmount = entry.ReceivedAmount,
                            CreatedDate = DateTime.Now
                        };

                        await _weeklyDebtRepository.AddAsync(newDebt);
                        savedCount++;
                    }
                }

                // إعادة تحميل البيانات
                await LoadWeeklyData();

                var message = $"تم حفظ البيانات بنجاح!\n";
                if (savedCount > 0) message += $"سجلات جديدة: {savedCount}\n";
                if (updatedCount > 0) message += $"سجلات محدثة: {updatedCount}";

                MessageBox.Show(
                    message,
                    "نجح الحفظ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );

                lblStatus.Text = $"تم حفظ البيانات - جديد: {savedCount}, محدث: {updatedCount}";
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في حفظ البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
                lblStatus.Text = "خطأ في حفظ البيانات";
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "💾 حفظ البيانات";
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateData()
        {
            var entriesWithData = _weeklyEntries.Where(e => e.HasData).ToList();

            if (!entriesWithData.Any())
            {
                MessageBox.Show(
                    "لا توجد بيانات للحفظ",
                    "تنبيه",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                return false;
            }

            // التحقق من وجود قيم سالبة
            foreach (var entry in entriesWithData)
            {
                if (entry.SamirAmount < 0 || entry.MaherAmount < 0 || entry.RaidAmount < 0 ||
                    entry.HaiderAmount < 0 || entry.LateAmount < 0 || entry.ReceivedAmount < 0)
                {
                    MessageBox.Show(
                        $"توجد قيم سالبة في بيانات الرعوي '{entry.RaayahName}'\nيرجى التأكد من صحة البيانات",
                        "خطأ في البيانات",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// مسح البيانات
        /// </summary>
        private void btnClear_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد مسح جميع البيانات المدخلة؟\n\nملاحظة: لن يتم حذف البيانات المحفوظة مسبقاً",
                "تأكيد المسح",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (result == DialogResult.Yes)
            {
                foreach (var entry in _weeklyEntries)
                {
                    if (!entry.ExistingId.HasValue)
                    {
                        entry.SamirAmount = 0;
                        entry.MaherAmount = 0;
                        entry.RaidAmount = 0;
                        entry.HaiderAmount = 0;
                        entry.LateAmount = 0;
                        entry.ReceivedAmount = 0;
                    }
                }

                dgvWeeklyData.Refresh();
                UpdateTotals();
                UpdateButtonStates();
                lblStatus.Text = "تم مسح البيانات الجديدة";
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadWeeklyData();
            lblStatus.Text = "تم تحديث البيانات";
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        private void WeeklyDataEntryForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var hasUnsavedData = _weeklyEntries?.Any(entry =>
                !entry.ExistingId.HasValue && entry.HasData) ?? false;

            if (hasUnsavedData)
            {
                var result = MessageBox.Show(
                    "توجد بيانات غير محفوظة. هل تريد إغلاق النافذة بدون حفظ؟",
                    "تأكيد الإغلاق",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
    }

    /// <summary>
    /// فئة مساعدة لإدخال البيانات الأسبوعية
    /// </summary>
    public class WeeklyDebtEntry
    {
        public int RaayahId { get; set; }
        public string RaayahName { get; set; } = string.Empty;
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public decimal SamirAmount { get; set; }
        public decimal MaherAmount { get; set; }
        public decimal RaidAmount { get; set; }
        public decimal HaiderAmount { get; set; }
        public decimal LateAmount { get; set; }
        public decimal ReceivedAmount { get; set; }
        public int? ExistingId { get; set; }

        public decimal TotalDebt => SamirAmount + MaherAmount + RaidAmount + HaiderAmount + LateAmount;
        public decimal NetAmount => TotalDebt - ReceivedAmount;
        public bool HasData => SamirAmount > 0 || MaherAmount > 0 || RaidAmount > 0 || HaiderAmount > 0 || LateAmount > 0 || ReceivedAmount > 0;

        /// <summary>
        /// إعادة حساب المبالغ (يمكن استخدامها لاحقاً للحسابات المعقدة)
        /// </summary>
        public void RecalculateAmounts()
        {
            // حالياً لا توجد حسابات إضافية مطلوبة
            // يمكن إضافة منطق حساب الخصومات هنا لاحقاً
        }
    }
}
