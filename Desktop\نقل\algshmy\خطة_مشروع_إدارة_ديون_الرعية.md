# 📋 خطة مشروع إدارة ديون الرعية - شركة الغشمي

## 🎯 نظرة عامة على المشروع

**الهدف:** تطوير تطبيق سطح مكتب باستخدام C# Windows Forms لإدارة ديون الرعية في أربعة فروع (سمير، ماهر، رايد، حيدر) مع نظام تقارير أسبوعية.

**التقنيات المستخدمة:**
- C# Windows Forms Application
- SQLite Database
- Entity Framework Core
- Repository Pattern
- دعم اللغة العربية (RTL)

---

## 🏗️ المرحلة الأولى: إعداد المشروع والبنية الأساسية

### 1. إنشاء المشروع
- [ ] إنشاء Solution جديد باسم `AlGhashmiDebtManagement`
- [ ] إعداد المشروع لدعم .NET 6/7/8
- [ ] إضافة المراجع المطلوبة (Entity Framework Core, SQLite)

### 2. بنية المجلدات والملفات
```
AlGhashmiDebtManagement/
├── Models/                    # نماذج البيانات
│   ├── Raayah.cs
│   ├── WeeklyDebt.cs
│   └── ReportModels.cs
├── Data/                      # طبقة البيانات
│   ├── AppDbContext.cs
│   ├── DatabaseInitializer.cs
│   └── Repositories/
│       ├── IRaayahRepository.cs
│       ├── RaayahRepository.cs
│       ├── IWeeklyDebtRepository.cs
│       └── WeeklyDebtRepository.cs
├── Forms/                     # النوافذ
│   ├── MainForm.cs
│   ├── WeeklyDataEntryForm.cs
│   ├── RaayahManagementForm.cs
│   └── ReportsForm.cs
├── Services/                  # خدمات العمل
│   ├── DebtCalculationService.cs
│   ├── ReportService.cs
│   └── ExportService.cs
├── Helpers/                   # مساعدات
│   ├── ArabicHelper.cs
│   ├── CurrencyHelper.cs
│   └── DateHelper.cs
└── Resources/                 # الموارد
    └── Strings.resx
```

### 3. الحزم المطلوبة (NuGet Packages)
- [ ] `Microsoft.EntityFrameworkCore.Sqlite`
- [ ] `Microsoft.EntityFrameworkCore.Tools`
- [ ] `Microsoft.EntityFrameworkCore.Design`
- [ ] `iTextSharp` أو `PdfSharp` (للتصدير PDF)

---

## 🗄️ المرحلة الثانية: تصميم قاعدة البيانات

### 4. نماذج البيانات (Models)

#### 4.1 جدول الرعية (Raayah)
```csharp
- Id (int, primary key)
- FullName (string)
- EnableDiscount (bool): لتفعيل خصم الحوالة
- InKashfOzri (bool): لإظهار الرعوي في كشف الأوزري
- InKharijKashf (bool): لإظهاره في خارج الكشف
- CreatedDate (DateTime)
- IsActive (bool)
```

#### 4.2 جدول الديون الأسبوعية (WeeklyDebts)
```csharp
- Id (int, primary key)
- RaayahId (foreign key إلى جدول الرعية)
- DateFrom (DateTime): تاريخ بداية الأسبوع
- DateTo (DateTime): تاريخ نهاية الأسبوع
- SamirAmount (decimal): مبلغ فرع سمير
- MaherAmount (decimal): مبلغ فرع ماهر
- RaidAmount (decimal): مبلغ فرع رايد
- HaiderAmount (decimal): مبلغ فرع حيدر
- LateAmount (decimal): مبلغ متأخر
- ReceivedAmount (decimal): مبلغ واصل
- CreatedDate (DateTime)
```

### 5. إعداد Entity Framework Core
- [ ] `AppDbContext.cs`: سياق قاعدة البيانات
- [ ] إعداد العلاقات بين الجداول (One-to-Many)
- [ ] إعداد SQLite كقاعدة بيانات
- [ ] إنشاء Migrations الأولية

---

## 🖥️ المرحلة الثالثة: تطوير واجهات المستخدم

### 6. النافذة الرئيسية (MainForm)
- [ ] تصميم قائمة تنقل رئيسية
- [ ] أزرار للوصول لجميع الوظائف:
  - إدخال البيانات الأسبوعية
  - إدارة الرعية
  - التقارير
  - الإعدادات
- [ ] شريط حالة يعرض معلومات النظام
- [ ] دعم RTL للغة العربية

### 7. نافذة إدخال البيانات الأسبوعية (WeeklyDataEntryForm)
- [ ] منتقي التاريخ (من - إلى) لمدة 7 أيام
- [ ] DataGridView لعرض الرعية مع أعمدة:
  - اسم الرعوي
  - مبلغ سمير
  - مبلغ ماهر
  - مبلغ رايد
  - مبلغ حيدر
  - المبلغ المتأخر
  - المبلغ الواصل
- [ ] أزرار الحفظ والإلغاء والتحديث
- [ ] التحقق من صحة البيانات
- [ ] رسائل التأكيد والأخطاء

### 8. نافذة إدارة الرعية (RaayahManagementForm)
- [ ] DataGridView لعرض وتعديل بيانات الرعية
- [ ] أعمدة الجدول:
  - الاسم الكامل
  - تفعيل خصم الحوالة (CheckBox)
  - في كشف الأوزري (CheckBox)
  - في خارج الكشف (CheckBox)
  - تاريخ الإنشاء
  - الحالة (نشط/غير نشط)
- [ ] أزرار إضافة/تعديل/حذف/حفظ
- [ ] نافذة فرعية لإضافة/تعديل الرعوي

### 9. نافذة التقارير (ReportsForm)
- [ ] منتقي التاريخ (من - إلى)
- [ ] ComboBox لاختيار نوع التقرير:
  - التقرير الكامل
  - التقرير مع خصم الحوالة
  - كشف بطاقات
  - كشف الأوزري
  - خارج الكشف
- [ ] DataGridView لعرض نتائج التقرير
- [ ] أزرار توليد التقرير والتصدير
- [ ] معاينة الطباعة

---

## ⚙️ المرحلة الرابعة: تطوير منطق العمل

### 10. طبقة المستودعات (Repository Layer)
- [ ] `IRaayahRepository` و `RaayahRepository`
  - GetAll(), GetById(), Add(), Update(), Delete()
  - GetActiveRaayah(), GetByKashfType()
- [ ] `IWeeklyDebtRepository` و `WeeklyDebtRepository`
  - GetByDateRange(), GetByRaayahId()
  - AddWeeklyDebts(), UpdateWeeklyDebts()

### 11. خدمات الحسابات والتقارير
- [ ] `DebtCalculationService`
  - حساب إجمالي الديون لكل رعوي
  - حساب خصم الحوالة (3%)
  - حساب الصافي (الديون - الواصل)
- [ ] `ReportService`
  - توليد التقرير الكامل
  - توليد تقرير خصم الحوالة
  - توليد كشف البطاقات
  - توليد كشف الأوزري
  - توليد خارج الكشف
- [ ] `ExportService`
  - تصدير إلى Excel
  - تصدير إلى PDF
  - طباعة التقارير

---

## 🎨 المرحلة الخامسة: تصميم الشاشات والواجهات العربية

### 12. تصميم واجهات المستخدم العربية

#### 12.1 المبادئ العامة للتصميم العربي
- [ ] **اتجاه النص:** جميع النوافذ RightToLeft = Yes
- [ ] **الخطوط:** استخدام خطوط عربية واضحة (Tahoma, Arial Unicode MS)
- [ ] **الألوان:** ألوان هادئة ومريحة للعين (أزرق فاتح، رمادي، أبيض)
- [ ] **التخطيط:** ترتيب العناصر من اليمين إلى اليسار
- [ ] **الأزرار:** نصوص عربية واضحة مع أيقونات مفهومة

#### 12.2 تصميم النافذة الرئيسية (MainForm)
```
┌─────────────────────────────────────────────────────────────┐
│  نظام إدارة ديون الرعية - شركة الغشمي                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   📊 التقارير   │  │  👥 إدارة الرعية │  │ 📝 إدخال البيانات │ │
│  │                │  │                │  │    الأسبوعية    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  🔧 الإعدادات   │  │  💾 نسخ احتياطي │  │   📈 الإحصائيات  │ │
│  │                │  │                │  │                │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ الحالة: متصل | آخر تحديث: 2024/01/15 | المستخدم: المدير      │
└─────────────────────────────────────────────────────────────┘
```

#### 12.3 تصميم نافذة إدخال البيانات الأسبوعية
```
┌─────────────────────────────────────────────────────────────┐
│  إدخال البيانات الأسبوعية                                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  من تاريخ: [____/____/____]  إلى تاريخ: [____/____/____]    │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ اسم الرعوي │ سمير │ ماهر │ رايد │ حيدر │ متأخر │ واصل │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ أحمد محمد   │ 1000 │ 1500 │ 800  │ 1200 │ 500   │ 2000 │ │
│ │ علي حسن     │ 900  │ 1100 │ 700  │ 1000 │ 300   │ 1800 │ │
│ │ محمد سالم   │ 1200 │ 1300 │ 900  │ 1100 │ 400   │ 2200 │ │
│ │ ...         │ ...  │ ...  │ ...  │ ...  │ ...   │ ...  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│           [💾 حفظ البيانات]  [🔄 تحديث]  [❌ إلغاء]          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 12.4 تصميم نافذة إدارة الرعية
```
┌─────────────────────────────────────────────────────────────┐
│  إدارة بيانات الرعية                                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  [🔍 بحث: ________________]  [➕ إضافة رعوي جديد]           │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ الاسم الكامل │ خصم الحوالة │ كشف الأوزري │ خارج الكشف │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ أحمد محمد علي │     ☑      │      ☑      │      ☐      │ │
│ │ علي حسن سالم  │     ☐      │      ☑      │      ☐      │ │
│ │ محمد سالم أحمد │     ☑      │      ☐      │      ☑      │ │
│ │ ...          │     ...    │      ...    │      ...    │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│        [✏️ تعديل]  [🗑️ حذف]  [💾 حفظ التغييرات]            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 12.5 تصميم نافذة التقارير
```
┌─────────────────────────────────────────────────────────────┐
│  التقارير والكشوفات                                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  من تاريخ: [____/____/____]  إلى تاريخ: [____/____/____]    │
│                                                             │
│  نوع التقرير: [التقرير الكامل ▼]                            │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                    نتائج التقرير                        │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ الرعوي │ إجمالي الديون │ الواصل │ الصافي │ خصم الحوالة │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ أحمد   │    5000       │  2000  │  3000  │    90      │ │
│ │ علي    │    4000       │  1800  │  2200  │    66      │ │
│ │ محمد   │    4500       │  2200  │  2300  │    69      │ │
│ │ المجموع │   13500       │  6000  │  7500  │   225      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│     [📊 توليد التقرير]  [📄 تصدير PDF]  [📋 تصدير Excel]    │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 13. مواصفات التصميم التفصيلية

#### 13.1 الخطوط والألوان
- [ ] **الخط الأساسي:** Tahoma 12pt للنصوص العادية
- [ ] **خط العناوين:** Tahoma Bold 14pt للعناوين
- [ ] **خط الأرقام:** Tahoma 11pt للبيانات الرقمية
- [ ] **اللون الأساسي:** #2E86AB (أزرق)
- [ ] **اللون الثانوي:** #A23B72 (بنفسجي)
- [ ] **لون الخلفية:** #F18F01 (برتقالي فاتح)
- [ ] **لون النص:** #212529 (رمادي داكن)

#### 13.2 أحجام النوافذ والعناصر
- [ ] **النافذة الرئيسية:** 1000x700 بكسل
- [ ] **نافذة إدخال البيانات:** 1200x800 بكسل
- [ ] **نافذة إدارة الرعية:** 900x600 بكسل
- [ ] **نافذة التقارير:** 1100x750 بكسل
- [ ] **ارتفاع الأزرار:** 35 بكسل
- [ ] **عرض الأزرار:** 120 بكسل
- [ ] **المسافات بين العناصر:** 10 بكسل

#### 13.3 الأيقونات والرموز
- [ ] **إدخال البيانات:** 📝 أو 💾
- [ ] **إدارة الرعية:** 👥 أو 👤
- [ ] **التقارير:** 📊 أو 📈
- [ ] **الإعدادات:** ⚙️ أو 🔧
- [ ] **حفظ:** 💾
- [ ] **إلغاء:** ❌
- [ ] **تعديل:** ✏️
- [ ] **حذف:** 🗑️
- [ ] **بحث:** 🔍
- [ ] **طباعة:** 🖨️

### 14. دعم اللغة العربية المتقدم
- [ ] **اتجاه النص:** RightToLeft = Yes لجميع النوافذ
- [ ] **ترقيم الصفحات:** باللغة العربية
- [ ] **تنسيق التاريخ:** dd/MM/yyyy (عربي)
- [ ] **تنسيق العملة:** ريال يمني (YER) أو ريال سعودي (SAR)
- [ ] **رسائل التأكيد:** باللغة العربية
- [ ] **رسائل الأخطاء:** باللغة العربية
- [ ] **تلميحات الأدوات:** باللغة العربية

---

## 🛡️ المرحلة السادسة: التحسينات والميزات الإضافية

### 15. التحقق من البيانات والأمان
- [ ] التحقق من صحة المدخلات (Validation)
- [ ] معالجة الأخطاء (Exception Handling)
- [ ] رسائل تأكيد للعمليات الحساسة باللغة العربية
- [ ] نسخ احتياطية لقاعدة البيانات
- [ ] تشفير البيانات الحساسة

### 16. ميزات إضافية
- [ ] البحث والتصفية في الجداول
- [ ] إعدادات التطبيق (Settings)
- [ ] سجل العمليات (Audit Log)
- [ ] إحصائيات سريعة في الشاشة الرئيسية
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تصدير البيانات بصيغ متعددة

### 17. تحسينات الأداء وسهولة الاستخدام
- [ ] **تحميل البيانات بشكل تدريجي** (Lazy Loading)
- [ ] **ذاكرة التخزين المؤقت** للبيانات المتكررة
- [ ] **اختصارات لوحة المفاتيح** للعمليات الشائعة
- [ ] **حفظ تلقائي** للبيانات أثناء الإدخال
- [ ] **استعادة الجلسة** عند إعادة فتح التطبيق
- [ ] **رسائل تقدم العمليات** للعمليات الطويلة

---

## 🔄 ترتيب التنفيذ المقترح المحدث

### المرحلة الأولى (الأسبوع 1-2)
1. **إعداد المشروع والبنية الأساسية**
   - إنشاء المشروع وإعداد NuGet Packages
   - تصميم نماذج البيانات وقاعدة البيانات
   - إعداد Entity Framework Core

2. **تصميم الواجهات الأساسية**
   - تصميم النافذة الرئيسية بالتخطيط العربي
   - إعداد الخطوط والألوان العربية
   - تطبيق RightToLeft على جميع النوافذ

### المرحلة الثانية (الأسبوع 3-4)
3. **تطوير نوافذ الإدخال والإدارة**
   - نافذة إدارة الرعية مع التصميم العربي
   - نافذة إدخال البيانات الأسبوعية
   - تطبيق التحقق من صحة البيانات

4. **تطوير طبقة البيانات**
   - Repository Pattern
   - خدمات الحسابات الأساسية

### المرحلة الثالثة (الأسبوع 5-6)
5. **تطوير نظام التقارير**
   - نافذة التقارير مع التصميم العربي
   - خدمات توليد التقارير المختلفة
   - تصدير PDF و Excel

6. **التحسينات والميزات الإضافية**
   - نظام البحث والتصفية
   - الإعدادات والنسخ الاحتياطي
   - تحسينات الأداء

---

## ✅ قائمة المراجعة النهائية

### الوظائف الأساسية
- [ ] جميع النوافذ تعمل بشكل صحيح مع التصميم العربي
- [ ] قاعدة البيانات تحفظ وتسترجع البيانات بدقة
- [ ] التقارير تُحسب بشكل صحيح (الديون، الخصومات، الصافي)
- [ ] نظام إدخال البيانات الأسبوعية يعمل بكفاءة

### دعم اللغة العربية
- [ ] جميع النوافذ تدعم RightToLeft
- [ ] الخطوط العربية تظهر بوضوح
- [ ] تنسيق التاريخ والعملة عربي
- [ ] جميع النصوص والرسائل باللغة العربية
- [ ] الأيقونات والرموز مناسبة للثقافة العربية

### الميزات المتقدمة
- [ ] التصدير إلى PDF و Excel يعمل
- [ ] نظام البحث والتصفية فعال
- [ ] النسخ الاحتياطي التلقائي يعمل
- [ ] معالجة الأخطاء شاملة ومفهومة
- [ ] الأداء سريع ومستقر

### الاختبار والتوثيق
- [ ] اختبار شامل لجميع الوظائف
- [ ] اختبار التصميم على دقات شاشة مختلفة
- [ ] توثيق المشروع ودليل المستخدم
- [ ] تدريب المستخدمين على النظام

---

## 📋 ملاحظات مهمة للتطوير

### قواعد الإدخال
- يتم إدخال بيانات كل الأسبوع في يوم واحد فقط
- جميع العمليات مرتبطة بنفس التاريخ الأسبوعي (من - إلى)
- لا يمكن تعديل البيانات بعد حفظها إلا بصلاحية خاصة

### قواعد الحسابات
- حساب الخصومات يعتمد على إعدادات كل رعوي من صفحة الإدارة
- خصم الحوالة = (إجمالي الديون - الواصل) × 3%
- الصافي = إجمالي الديون - الواصل
- إجمالي الديون = سمير + ماهر + رايد + حيدر + المتأخر

### قواعد التقارير
- التقرير الكامل: يشمل جميع الرعية
- كشف الأوزري: فقط الرعية المحددين في الإعدادات
- خارج الكشف: فقط الرعية المحددين لهذا النوع
- كشف البطاقات: بطاقة منفصلة لكل رعوي

### متطلبات التصميم
- الواجهة تدعم الكتابة من اليمين إلى اليسار
- استخدام ألوان هادئة ومريحة للعين
- خطوط عربية واضحة وقابلة للقراءة
- أيقونات مفهومة ومناسبة للثقافة العربية
- تخطيط منطقي وسهل الاستخدام
