using Microsoft.EntityFrameworkCore;
using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Data.Repositories
{
    /// <summary>
    /// تطبيق مستودع الرعية
    /// </summary>
    public class RaayahRepository : IRaayahRepository
    {
        private readonly AppDbContext _context;

        /// <summary>
        /// منشئ مستودع الرعية
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public RaayahRepository(AppDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// الحصول على جميع الرعية
        /// </summary>
        public async Task<IEnumerable<Raayah>> GetAllAsync(bool includeInactive = false)
        {
            var query = _context.Raayah.AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(r => r.IsActive);
            }

            return await query.OrderBy(r => r.FullName).ToListAsync();
        }

        /// <summary>
        /// الحصول على رعوي بالمعرف
        /// </summary>
        public async Task<Raayah?> GetByIdAsync(int id)
        {
            return await _context.Raayah.FindAsync(id);
        }

        /// <summary>
        /// الحصول على رعوي بالاسم
        /// </summary>
        public async Task<Raayah?> GetByNameAsync(string fullName)
        {
            return await _context.Raayah
                .FirstOrDefaultAsync(r => r.FullName == fullName);
        }

        /// <summary>
        /// البحث في الرعية بالاسم
        /// </summary>
        public async Task<IEnumerable<Raayah>> SearchByNameAsync(string searchTerm, bool includeInactive = false)
        {
            var query = _context.Raayah.AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(r => r.IsActive);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(r => r.FullName.Contains(searchTerm));
            }

            return await query.OrderBy(r => r.FullName).ToListAsync();
        }

        /// <summary>
        /// الحصول على الرعية النشطين فقط
        /// </summary>
        public async Task<IEnumerable<Raayah>> GetActiveAsync()
        {
            return await _context.Raayah
                .Where(r => r.IsActive)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الرعية في كشف الأوزري
        /// </summary>
        public async Task<IEnumerable<Raayah>> GetKashfOzriAsync()
        {
            return await _context.Raayah
                .Where(r => r.IsActive && r.InKashfOzri)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الرعية في خارج الكشف
        /// </summary>
        public async Task<IEnumerable<Raayah>> GetKharijKashfAsync()
        {
            return await _context.Raayah
                .Where(r => r.IsActive && r.InKharijKashf)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الرعية الذين لديهم خصم حوالة مفعل
        /// </summary>
        public async Task<IEnumerable<Raayah>> GetWithDiscountEnabledAsync()
        {
            return await _context.Raayah
                .Where(r => r.IsActive && r.EnableDiscount)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة رعوي جديد
        /// </summary>
        public async Task<Raayah> AddAsync(Raayah raayah)
        {
            if (raayah == null)
                throw new ArgumentNullException(nameof(raayah));

            raayah.CreatedDate = DateTime.Now;
            _context.Raayah.Add(raayah);
            await _context.SaveChangesAsync();
            return raayah;
        }

        /// <summary>
        /// تحديث بيانات رعوي
        /// </summary>
        public async Task<Raayah> UpdateAsync(Raayah raayah)
        {
            if (raayah == null)
                throw new ArgumentNullException(nameof(raayah));

            raayah.LastModifiedDate = DateTime.Now;
            _context.Raayah.Update(raayah);
            await _context.SaveChangesAsync();
            return raayah;
        }

        /// <summary>
        /// حذف رعوي (حذف ناعم - تعطيل)
        /// </summary>
        public async Task<bool> SoftDeleteAsync(int id)
        {
            var raayah = await GetByIdAsync(id);
            if (raayah == null)
                return false;

            raayah.IsActive = false;
            raayah.LastModifiedDate = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف رعوي نهائياً
        /// </summary>
        public async Task<bool> DeleteAsync(int id)
        {
            var raayah = await GetByIdAsync(id);
            if (raayah == null)
                return false;

            _context.Raayah.Remove(raayah);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// استعادة رعوي محذوف (تفعيل)
        /// </summary>
        public async Task<bool> RestoreAsync(int id)
        {
            var raayah = await GetByIdAsync(id);
            if (raayah == null)
                return false;

            raayah.IsActive = true;
            raayah.LastModifiedDate = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// التحقق من وجود رعوي بنفس الاسم
        /// </summary>
        public async Task<bool> ExistsAsync(string fullName, int? excludeId = null)
        {
            var query = _context.Raayah.Where(r => r.FullName == fullName);

            if (excludeId.HasValue)
            {
                query = query.Where(r => r.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// الحصول على عدد الرعية
        /// </summary>
        public async Task<int> GetCountAsync(bool includeInactive = false)
        {
            var query = _context.Raayah.AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(r => r.IsActive);
            }

            return await query.CountAsync();
        }

        /// <summary>
        /// الحصول على الرعية مع ديونهم الأسبوعية
        /// </summary>
        public async Task<IEnumerable<Raayah>> GetWithWeeklyDebtsAsync(bool includeInactive = false)
        {
            var query = _context.Raayah.Include(r => r.WeeklyDebts).AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(r => r.IsActive);
            }

            return await query.OrderBy(r => r.FullName).ToListAsync();
        }

        /// <summary>
        /// الحصول على رعوي مع ديونه الأسبوعية
        /// </summary>
        public async Task<Raayah?> GetWithWeeklyDebtsByIdAsync(int id)
        {
            return await _context.Raayah
                .Include(r => r.WeeklyDebts)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        /// <summary>
        /// تحديث إعدادات متعددة للرعية
        /// </summary>
        public async Task<int> BulkUpdateSettingsAsync(IEnumerable<(int Id, bool EnableDiscount, bool InKashfOzri, bool InKharijKashf)> updates)
        {
            int updatedCount = 0;

            foreach (var update in updates)
            {
                var raayah = await GetByIdAsync(update.Id);
                if (raayah != null)
                {
                    raayah.EnableDiscount = update.EnableDiscount;
                    raayah.InKashfOzri = update.InKashfOzri;
                    raayah.InKharijKashf = update.InKharijKashf;
                    raayah.LastModifiedDate = DateTime.Now;
                    updatedCount++;
                }
            }

            if (updatedCount > 0)
            {
                await _context.SaveChangesAsync();
            }

            return updatedCount;
        }

        /// <summary>
        /// حفظ التغييرات
        /// </summary>
        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }
    }
}
