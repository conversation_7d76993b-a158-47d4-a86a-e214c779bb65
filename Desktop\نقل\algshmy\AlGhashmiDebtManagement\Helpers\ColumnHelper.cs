using System.Drawing;
using System.Windows.Forms;

namespace AlGhashmiDebtManagement.Helpers
{
    /// <summary>
    /// مساعد إدارة الأعمدة المعربة
    /// </summary>
    public static class ColumnHelper
    {
        /// <summary>
        /// تعريفات الأعمدة المعربة
        /// </summary>
        public static class Columns
        {
            // أعمدة الرعية
            public static class Raayah
            {
                public const string Id = "المعرف";
                public const string FullName = "الاسم_الكامل";
                public const string EnableDiscount = "خصم_الحوالة";
                public const string InKashfOzri = "كشف_الأوزري";
                public const string InKharijKashf = "خارج_الكشف";
                public const string CreatedDate = "تاريخ_الإنشاء";
                public const string IsActive = "نشط";
                public const string Notes = "الملاحظات";
            }

            // أعمدة البيانات الأسبوعية
            public static class WeeklyDebt
            {
                public const string RaayahId = "معرف_الرعوي";
                public const string RaayahName = "اسم_الرعوي";
                public const string DateFrom = "من_تاريخ";
                public const string DateTo = "إلى_تاريخ";
                public const string SamirAmount = "فرع_سمير";
                public const string MaherAmount = "فرع_ماهر";
                public const string RaidAmount = "فرع_رايد";
                public const string HaiderAmount = "فرع_حيدر";
                public const string LateAmount = "متأخر";
                public const string ReceivedAmount = "واصل";
                public const string TotalDebt = "إجمالي_الديون";
                public const string NetAmount = "الصافي";
            }

            // أعمدة التقارير
            public static class Report
            {
                public const string RaayahName = "اسم_الرعوي";
                public const string SamirAmount = "فرع_سمير";
                public const string MaherAmount = "فرع_ماهر";
                public const string RaidAmount = "فرع_رايد";
                public const string HaiderAmount = "فرع_حيدر";
                public const string LateAmount = "متأخر";
                public const string ReceivedAmount = "واصل";
                public const string TotalDebtAmount = "إجمالي_الديون";
                public const string NetAmount = "الصافي";
                public const string DiscountAmount = "خصم_الحوالة";
                public const string NetAfterDiscount = "الصافي_بعد_الخصم";
            }
        }

        /// <summary>
        /// النصوص المعروضة للمستخدم
        /// </summary>
        public static class Headers
        {
            // رؤوس أعمدة الرعية
            public static class Raayah
            {
                public const string Id = "المعرف";
                public const string FullName = "الاسم الكامل";
                public const string EnableDiscount = "خصم الحوالة";
                public const string InKashfOzri = "كشف الأوزري";
                public const string InKharijKashf = "خارج الكشف";
                public const string CreatedDate = "تاريخ الإنشاء";
                public const string IsActive = "نشط";
                public const string Notes = "الملاحظات";
            }

            // رؤوس أعمدة البيانات الأسبوعية
            public static class WeeklyDebt
            {
                public const string RaayahId = "معرف الرعوي";
                public const string RaayahName = "اسم الرعوي";
                public const string DateFrom = "من تاريخ";
                public const string DateTo = "إلى تاريخ";
                public const string SamirAmount = "سمير";
                public const string MaherAmount = "ماهر";
                public const string RaidAmount = "رايد";
                public const string HaiderAmount = "حيدر";
                public const string LateAmount = "متأخر";
                public const string ReceivedAmount = "واصل";
                public const string TotalDebt = "إجمالي الديون";
                public const string NetAmount = "الصافي";
            }

            // رؤوس أعمدة التقارير
            public static class Report
            {
                public const string RaayahName = "اسم الرعوي";
                public const string SamirAmount = "سمير";
                public const string MaherAmount = "ماهر";
                public const string RaidAmount = "رايد";
                public const string HaiderAmount = "حيدر";
                public const string LateAmount = "متأخر";
                public const string ReceivedAmount = "واصل";
                public const string TotalDebtAmount = "إجمالي الديون";
                public const string NetAmount = "الصافي";
                public const string DiscountAmount = "خصم الحوالة";
                public const string NetAfterDiscount = "الصافي بعد الخصم";
            }
        }

        /// <summary>
        /// إنشاء عمود نصي معرب
        /// </summary>
        public static DataGridViewTextBoxColumn CreateTextColumn(
            string name, 
            string dataPropertyName, 
            string headerText, 
            int width = 100, 
            bool readOnly = false,
            bool visible = true,
            string format = "")
        {
            var column = new DataGridViewTextBoxColumn
            {
                Name = name,
                DataPropertyName = dataPropertyName,
                HeaderText = headerText,
                Width = width,
                ReadOnly = readOnly,
                Visible = visible
            };

            if (!string.IsNullOrEmpty(format))
            {
                column.DefaultCellStyle = new DataGridViewCellStyle { Format = format };
            }

            return column;
        }

        /// <summary>
        /// إنشاء عمود رقمي معرب
        /// </summary>
        public static DataGridViewTextBoxColumn CreateNumericColumn(
            string name,
            string dataPropertyName,
            string headerText,
            int width = 100,
            bool readOnly = false,
            Color? headerColor = null,
            Color? backgroundColor = null)
        {
            var column = new DataGridViewTextBoxColumn
            {
                Name = name,
                DataPropertyName = dataPropertyName,
                HeaderText = headerText,
                Width = width,
                ReadOnly = readOnly,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N0",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            };

            if (backgroundColor.HasValue)
            {
                column.DefaultCellStyle.BackColor = backgroundColor.Value;
            }

            // ملاحظة: headerColor يُستخدم لتلوين رأس العمود ويتم تطبيقه في ApplyArabicFormatting
            // أو يمكن تطبيقه مباشرة على العمود بعد إضافته للجدول

            return column;
        }

        /// <summary>
        /// إنشاء عمود خانة اختيار معرب
        /// </summary>
        public static DataGridViewCheckBoxColumn CreateCheckBoxColumn(
            string name, 
            string dataPropertyName, 
            string headerText, 
            int width = 100, 
            bool readOnly = false)
        {
            return new DataGridViewCheckBoxColumn
            {
                Name = name,
                DataPropertyName = dataPropertyName,
                HeaderText = headerText,
                Width = width,
                ReadOnly = readOnly
            };
        }

        /// <summary>
        /// إنشاء عمود تاريخ معرب
        /// </summary>
        public static DataGridViewTextBoxColumn CreateDateColumn(
            string name, 
            string dataPropertyName, 
            string headerText, 
            int width = 120, 
            bool readOnly = true,
            string format = "dd/MM/yyyy")
        {
            return new DataGridViewTextBoxColumn
            {
                Name = name,
                DataPropertyName = dataPropertyName,
                HeaderText = headerText,
                Width = width,
                ReadOnly = readOnly,
                DefaultCellStyle = new DataGridViewCellStyle { Format = format }
            };
        }

        /// <summary>
        /// تطبيق تنسيق عربي موحد على DataGridView
        /// </summary>
        public static void ApplyArabicFormatting(DataGridView dataGridView)
        {
            // إعداد الاتجاه من اليمين إلى اليسار
            dataGridView.RightToLeft = RightToLeft.Yes;
            
            // إعداد الخط العربي
            dataGridView.Font = new Font("Tahoma", 12F, FontStyle.Regular);
            
            // إعداد ألوان الرؤوس
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            
            // إعداد ألوان الصفوف
            dataGridView.DefaultCellStyle.BackColor = Color.White;
            dataGridView.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            
            // إعداد الصفوف المتناوبة
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            
            // إعداد الحدود
            dataGridView.GridColor = Color.FromArgb(222, 226, 230);
            dataGridView.BorderStyle = BorderStyle.Fixed3D;
            
            // إعداد عام
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridView.ColumnHeadersHeight = 40;
            dataGridView.RowTemplate.Height = 35;
        }
    }
}
