using AlGhashmiDebtManagement.Data.Repositories;
using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل الرعوي
    /// </summary>
    public partial class AddEditRaayahForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly Raayah? _existingRaayah;
        private readonly bool _isEditMode;

        /// <summary>
        /// منشئ لإضافة رعوي جديد
        /// </summary>
        public AddEditRaayahForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository ?? throw new ArgumentNullException(nameof(raayahRepository));
            _existingRaayah = null;
            _isEditMode = false;

            InitializeComponent();
            SetupArabicUI();
            SetupForAddMode();
        }

        /// <summary>
        /// منشئ لتعديل رعوي موجود
        /// </summary>
        public AddEditRaayahForm(IRaayahRepository raayahRepository, Ra<PERSON>h existingRaayah)
        {
            _raayahRepository = raayahRepository ?? throw new ArgumentNullException(nameof(raayahRepository));
            _existingRaayah = existingRaayah ?? throw new ArgumentNullException(nameof(existingRaayah));
            _isEditMode = true;

            InitializeComponent();
            SetupArabicUI();
            SetupForEditMode();
        }

        /// <summary>
        /// إعداد الواجهة العربية
        /// </summary>
        private void SetupArabicUI()
        {
            // إعداد اتجاه النص من اليمين إلى اليسار
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد الخط العربي
            this.Font = new Font("Tahoma", 12F, FontStyle.Regular);

            // إعداد الألوان
            this.BackColor = Color.FromArgb(248, 249, 250);
        }

        /// <summary>
        /// إعداد النافذة لوضع الإضافة
        /// </summary>
        private void SetupForAddMode()
        {
            this.Text = "إضافة رعوي جديد";
            lblTitle.Text = "إضافة رعوي جديد";
            btnSave.Text = "💾 إضافة";

            // إعداد القيم الافتراضية
            chkEnableDiscount.Checked = false;
            chkInKashfOzri.Checked = true;
            chkInKharijKashf.Checked = false;
            chkIsActive.Checked = true;
        }

        /// <summary>
        /// إعداد النافذة لوضع التعديل
        /// </summary>
        private void SetupForEditMode()
        {
            if (_existingRaayah == null) return;

            this.Text = "تعديل بيانات الرعوي";
            lblTitle.Text = "تعديل بيانات الرعوي";
            btnSave.Text = "💾 حفظ التغييرات";

            // تحميل البيانات الموجودة
            txtFullName.Text = _existingRaayah.FullName;
            chkEnableDiscount.Checked = _existingRaayah.EnableDiscount;
            chkInKashfOzri.Checked = _existingRaayah.InKashfOzri;
            chkInKharijKashf.Checked = _existingRaayah.InKharijKashf;
            chkIsActive.Checked = _existingRaayah.IsActive;
            txtNotes.Text = _existingRaayah.Notes ?? string.Empty;

            // عرض معلومات إضافية
            lblCreatedDate.Text = $"تاريخ الإنشاء: {_existingRaayah.CreatedDate:dd/MM/yyyy HH:mm}";
            if (_existingRaayah.LastModifiedDate.HasValue)
            {
                lblLastModified.Text = $"آخر تعديل: {_existingRaayah.LastModifiedDate:dd/MM/yyyy HH:mm}";
                lblLastModified.Visible = true;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show(
                    "يرجى إدخال الاسم الكامل للرعوي",
                    "خطأ في البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                txtFullName.Focus();
                return false;
            }

            if (txtFullName.Text.Trim().Length < 3)
            {
                MessageBox.Show(
                    "الاسم يجب أن يكون 3 أحرف على الأقل",
                    "خطأ في البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                txtFullName.Focus();
                return false;
            }

            if (txtFullName.Text.Trim().Length > 100)
            {
                MessageBox.Show(
                    "الاسم لا يجب أن يتجاوز 100 حرف",
                    "خطأ في البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                txtFullName.Focus();
                return false;
            }

            // التحقق من الملاحظات
            if (!string.IsNullOrEmpty(txtNotes.Text) && txtNotes.Text.Length > 500)
            {
                MessageBox.Show(
                    "الملاحظات لا يجب أن تتجاوز 500 حرف",
                    "خطأ في البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
                txtNotes.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// التحقق من تكرار الاسم
        /// </summary>
        private async Task<bool> CheckNameDuplication()
        {
            try
            {
                var fullName = txtFullName.Text.Trim();
                var excludeId = _isEditMode ? _existingRaayah?.Id : null;
                
                var exists = await _raayahRepository.ExistsAsync(fullName, excludeId);
                
                if (exists)
                {
                    MessageBox.Show(
                        $"يوجد رعوي آخر بنفس الاسم '{fullName}'\nيرجى اختيار اسم مختلف",
                        "اسم مكرر",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    txtFullName.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في التحقق من تكرار الاسم:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
                return false;
            }
        }

        /// <summary>
        /// حفظ البيانات
        /// </summary>
        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // التحقق من تكرار الاسم
                if (!await CheckNameDuplication())
                    return;

                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                if (_isEditMode && _existingRaayah != null)
                {
                    // تحديث الرعوي الموجود
                    _existingRaayah.FullName = txtFullName.Text.Trim();
                    _existingRaayah.EnableDiscount = chkEnableDiscount.Checked;
                    _existingRaayah.InKashfOzri = chkInKashfOzri.Checked;
                    _existingRaayah.InKharijKashf = chkInKharijKashf.Checked;
                    _existingRaayah.IsActive = chkIsActive.Checked;
                    _existingRaayah.Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim();

                    await _raayahRepository.UpdateAsync(_existingRaayah);

                    MessageBox.Show(
                        "تم تحديث بيانات الرعوي بنجاح",
                        "نجح التحديث",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    // إضافة رعوي جديد
                    var newRaayah = new Raayah
                    {
                        FullName = txtFullName.Text.Trim(),
                        EnableDiscount = chkEnableDiscount.Checked,
                        InKashfOzri = chkInKashfOzri.Checked,
                        InKharijKashf = chkInKharijKashf.Checked,
                        IsActive = chkIsActive.Checked,
                        Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim(),
                        CreatedDate = DateTime.Now
                    };

                    await _raayahRepository.AddAsync(newRaayah);

                    MessageBox.Show(
                        "تم إضافة الرعوي الجديد بنجاح",
                        "نجحت الإضافة",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في حفظ البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = _isEditMode ? "💾 حفظ التغييرات" : "💾 إضافة";
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// معالج تغيير النص في حقل الاسم
        /// </summary>
        private void txtFullName_TextChanged(object sender, EventArgs e)
        {
            // تحديث حالة زر الحفظ
            btnSave.Enabled = !string.IsNullOrWhiteSpace(txtFullName.Text);
        }

        /// <summary>
        /// معالج الضغط على مفتاح في حقل الاسم
        /// </summary>
        private void txtFullName_KeyPress(object sender, KeyPressEventArgs e)
        {
            // منع إدخال أحرف غير مرغوب فيها
            if (e.KeyChar == '\t' || e.KeyChar == '\n' || e.KeyChar == '\r')
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج تغيير حالة خارج الكشف
        /// </summary>
        private void chkInKharijKashf_CheckedChanged(object sender, EventArgs e)
        {
            // إذا تم تفعيل خارج الكشف، يتم تعطيل كشف الأوزري تلقائياً
            if (chkInKharijKashf.Checked)
            {
                chkInKashfOzri.Checked = false;
            }
        }

        /// <summary>
        /// معالج تغيير حالة كشف الأوزري
        /// </summary>
        private void chkInKashfOzri_CheckedChanged(object sender, EventArgs e)
        {
            // إذا تم تفعيل كشف الأوزري، يتم تعطيل خارج الكشف تلقائياً
            if (chkInKashfOzri.Checked)
            {
                chkInKharijKashf.Checked = false;
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        private void AddEditRaayahForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (this.DialogResult == DialogResult.None)
            {
                var result = MessageBox.Show(
                    "هل تريد إغلاق النافذة بدون حفظ؟",
                    "تأكيد الإغلاق",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
    }
}
