using AlGhashmiDebtManagement.Models;

namespace AlGhashmiDebtManagement.Services
{
    /// <summary>
    /// خدمة حساب الديون والخصومات
    /// </summary>
    public class DebtCalculationService
    {
        /// <summary>
        /// حساب إجمالي الديون لرعوي
        /// </summary>
        /// <param name="weeklyDebt">بيانات الدين الأسبوعي</param>
        /// <returns>إجمالي الديون</returns>
        public decimal CalculateTotalDebt(WeeklyDebt weeklyDebt)
        {
            if (weeklyDebt == null)
                return 0;

            return weeklyDebt.SamirAmount + weeklyDebt.MaherAmount + 
                   weeklyDebt.RaidAmount + weeklyDebt.HaiderAmount + weeklyDebt.LateAmount;
        }

        /// <summary>
        /// حساب الصافي (إجمالي الديون - الواصل)
        /// </summary>
        /// <param name="weeklyDebt">بيانات الدين الأسبوعي</param>
        /// <returns>المبلغ الصافي</returns>
        public decimal CalculateNetAmount(WeeklyDebt weeklyDebt)
        {
            if (weeklyDebt == null)
                return 0;

            var totalDebt = CalculateTotalDebt(weeklyDebt);
            return totalDebt - weeklyDebt.ReceivedAmount;
        }

        /// <summary>
        /// حساب خصم الحوالة (3% من الصافي)
        /// </summary>
        /// <param name="weeklyDebt">بيانات الدين الأسبوعي</param>
        /// <param name="raayah">بيانات الرعوي</param>
        /// <returns>مبلغ خصم الحوالة</returns>
        public decimal CalculateDiscountAmount(WeeklyDebt weeklyDebt, Raayah raayah)
        {
            if (weeklyDebt == null || raayah == null || !raayah.EnableDiscount)
                return 0;

            var netAmount = CalculateNetAmount(weeklyDebt);
            return netAmount * 0.03m; // 3%
        }

        /// <summary>
        /// حساب الصافي بعد خصم الحوالة
        /// </summary>
        /// <param name="weeklyDebt">بيانات الدين الأسبوعي</param>
        /// <param name="raayah">بيانات الرعوي</param>
        /// <returns>الصافي بعد الخصم</returns>
        public decimal CalculateNetAfterDiscount(WeeklyDebt weeklyDebt, Raayah raayah)
        {
            if (weeklyDebt == null)
                return 0;

            var netAmount = CalculateNetAmount(weeklyDebt);
            var discountAmount = CalculateDiscountAmount(weeklyDebt, raayah);
            
            return netAmount - discountAmount;
        }

        /// <summary>
        /// حساب إجماليات مجموعة من الديون
        /// </summary>
        /// <param name="weeklyDebts">قائمة الديون الأسبوعية</param>
        /// <param name="raayahList">قائمة الرعية</param>
        /// <returns>ملخص الإجماليات</returns>
        public ReportSummary CalculateTotals(IEnumerable<WeeklyDebt> weeklyDebts, IEnumerable<Raayah> raayahList)
        {
            if (weeklyDebts == null || !weeklyDebts.Any())
                return new ReportSummary();

            var raayahDict = raayahList?.ToDictionary(r => r.Id) ?? new Dictionary<int, Raayah>();
            var summary = new ReportSummary();

            foreach (var debt in weeklyDebts)
            {
                // إجماليات الفروع
                summary.TotalSamirAmount += debt.SamirAmount;
                summary.TotalMaherAmount += debt.MaherAmount;
                summary.TotalRaidAmount += debt.RaidAmount;
                summary.TotalHaiderAmount += debt.HaiderAmount;
                summary.TotalLateAmount += debt.LateAmount;
                summary.TotalReceivedAmount += debt.ReceivedAmount;

                // حساب خصم الحوالة إذا كان مفعلاً للرعوي
                if (raayahDict.TryGetValue(debt.RaayahId, out var raayah))
                {
                    summary.TotalDiscountAmount += CalculateDiscountAmount(debt, raayah);
                }
            }

            summary.TotalRaayahCount = weeklyDebts.Count();
            summary.GeneratedDate = DateTime.Now;

            return summary;
        }

        /// <summary>
        /// تحويل الديون الأسبوعية إلى بيانات تقرير
        /// </summary>
        /// <param name="weeklyDebts">قائمة الديون الأسبوعية</param>
        /// <param name="raayahList">قائمة الرعية</param>
        /// <returns>قائمة بيانات التقرير</returns>
        public IEnumerable<ReportData> ConvertToReportData(IEnumerable<WeeklyDebt> weeklyDebts, IEnumerable<Raayah> raayahList)
        {
            if (weeklyDebts == null || !weeklyDebts.Any())
                return new List<ReportData>();

            var raayahDict = raayahList?.ToDictionary(r => r.Id) ?? new Dictionary<int, Raayah>();
            var reportDataList = new List<ReportData>();

            foreach (var debt in weeklyDebts)
            {
                var raayah = raayahDict.TryGetValue(debt.RaayahId, out var r) ? r : null;
                
                var reportData = new ReportData
                {
                    RaayahId = debt.RaayahId,
                    RaayahName = raayah?.FullName ?? "غير معروف",
                    SamirAmount = debt.SamirAmount,
                    MaherAmount = debt.MaherAmount,
                    RaidAmount = debt.RaidAmount,
                    HaiderAmount = debt.HaiderAmount,
                    LateAmount = debt.LateAmount,
                    ReceivedAmount = debt.ReceivedAmount,
                    EnableDiscount = raayah?.EnableDiscount ?? false,
                    InKashfOzri = raayah?.InKashfOzri ?? false,
                    InKharijKashf = raayah?.InKharijKashf ?? false
                };

                reportDataList.Add(reportData);
            }

            return reportDataList.OrderBy(r => r.RaayahName);
        }

        /// <summary>
        /// تصفية بيانات التقرير حسب النوع
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>البيانات المصفاة</returns>
        public IEnumerable<ReportData> FilterReportData(IEnumerable<ReportData> reportData, ReportType reportType)
        {
            if (reportData == null)
                return new List<ReportData>();

            return reportType switch
            {
                ReportType.FullReport => reportData,
                ReportType.ReportWithDiscount => reportData.Where(r => r.EnableDiscount),
                ReportType.CardsReport => reportData,
                ReportType.KashfOzri => reportData.Where(r => r.InKashfOzri),
                ReportType.KharijKashf => reportData.Where(r => r.InKharijKashf),
                _ => reportData
            };
        }

        /// <summary>
        /// حساب النسب المئوية للفروع
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <returns>قاموس النسب المئوية</returns>
        public Dictionary<string, decimal> CalculateBranchPercentages(IEnumerable<ReportData> reportData)
        {
            if (reportData == null || !reportData.Any())
                return new Dictionary<string, decimal>();

            var totalSamir = reportData.Sum(r => r.SamirAmount);
            var totalMaher = reportData.Sum(r => r.MaherAmount);
            var totalRaid = reportData.Sum(r => r.RaidAmount);
            var totalHaider = reportData.Sum(r => r.HaiderAmount);
            var grandTotal = totalSamir + totalMaher + totalRaid + totalHaider;

            if (grandTotal == 0)
                return new Dictionary<string, decimal>();

            return new Dictionary<string, decimal>
            {
                ["سمير"] = Math.Round((totalSamir / grandTotal) * 100, 2),
                ["ماهر"] = Math.Round((totalMaher / grandTotal) * 100, 2),
                ["رايد"] = Math.Round((totalRaid / grandTotal) * 100, 2),
                ["حيدر"] = Math.Round((totalHaider / grandTotal) * 100, 2)
            };
        }

        /// <summary>
        /// تجميع البيانات حسب الرعوي (للفترات المتعددة)
        /// </summary>
        /// <param name="weeklyDebts">قائمة الديون الأسبوعية</param>
        /// <param name="raayahList">قائمة الرعية</param>
        /// <returns>البيانات المجمعة</returns>
        public IEnumerable<ReportData> AggregateByRaayah(IEnumerable<WeeklyDebt> weeklyDebts, IEnumerable<Raayah> raayahList)
        {
            if (weeklyDebts == null || !weeklyDebts.Any())
                return new List<ReportData>();

            var raayahDict = raayahList?.ToDictionary(r => r.Id) ?? new Dictionary<int, Raayah>();
            
            var aggregatedData = weeklyDebts
                .GroupBy(d => d.RaayahId)
                .Select(group =>
                {
                    var raayah = raayahDict.TryGetValue(group.Key, out var r) ? r : null;
                    
                    return new ReportData
                    {
                        RaayahId = group.Key,
                        RaayahName = raayah?.FullName ?? "غير معروف",
                        SamirAmount = group.Sum(d => d.SamirAmount),
                        MaherAmount = group.Sum(d => d.MaherAmount),
                        RaidAmount = group.Sum(d => d.RaidAmount),
                        HaiderAmount = group.Sum(d => d.HaiderAmount),
                        LateAmount = group.Sum(d => d.LateAmount),
                        ReceivedAmount = group.Sum(d => d.ReceivedAmount),
                        EnableDiscount = raayah?.EnableDiscount ?? false,
                        InKashfOzri = raayah?.InKashfOzri ?? false,
                        InKharijKashf = raayah?.InKharijKashf ?? false
                    };
                })
                .OrderBy(r => r.RaayahName);

            return aggregatedData;
        }

        /// <summary>
        /// التحقق من صحة بيانات التقرير
        /// </summary>
        /// <param name="criteria">معايير التقرير</param>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool ValidateReportCriteria(ReportCriteria criteria)
        {
            if (criteria == null)
                return false;

            // التحقق من التواريخ
            if (criteria.DateTo < criteria.DateFrom)
                return false;

            // التحقق من أن الفترة لا تتجاوز سنة واحدة
            if ((criteria.DateTo - criteria.DateFrom).Days > 365)
                return false;

            return true;
        }
    }
}
