@echo off
chcp 65001 > nul
echo ===============================
echo اختبار قاعدة البيانات
echo ===============================
echo.

echo التحقق من وجود قاعدة البيانات...
if exist "%LOCALAPPDATA%\AlGhashmiDebtManagement\AlGhashmiDebtManagement.db" (
    echo ✓ قاعدة البيانات موجودة
    echo حجم الملف:
    dir "%LOCALAPPDATA%\AlGhashmiDebtManagement\AlGhashmiDebtManagement.db" | find "AlGhashmiDebtManagement.db"
) else (
    echo ✗ قاعدة البيانات غير موجودة
)

echo.
echo محتويات مجلد قاعدة البيانات:
if exist "%LOCALAPPDATA%\AlGhashmiDebtManagement" (
    dir "%LOCALAPPDATA%\AlGhashmiDebtManagement"
) else (
    echo المجلد غير موجود
)

echo.
echo ===============================
echo انتهى الاختبار
echo ===============================
pause
