namespace AlGhashmiDebtManagement.Forms
{
    partial class ReportsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.pnlCriteria = new Panel();
            this.lblDateFrom = new Label();
            this.dtpDateFrom = new DateTimePicker();
            this.lblDateTo = new Label();
            this.dtpDateTo = new DateTimePicker();
            this.lblReportType = new Label();
            this.cmbReportType = new ComboBox();
            this.lblReportDescription = new Label();
            this.btnGenerateReport = new Button();
            this.pnlMain = new Panel();
            this.dgvReport = new DataGridView();
            this.pnlSummary = new Panel();
            this.lblSummaryTitle = new Label();
            this.lblTotalRaayah = new Label();
            this.lblTotalDebt = new Label();
            this.lblTotalReceived = new Label();
            this.lblNetAmount = new Label();
            this.lblDiscountAmount = new Label();
            this.lblNetAfterDiscount = new Label();
            this.pnlButtons = new Panel();
            this.btnExportPdf = new Button();
            this.btnExportExcel = new Button();
            this.btnPrint = new Button();
            this.btnPrintPreview = new Button();
            this.btnClose = new Button();
            this.pnlFooter = new Panel();
            this.lblStatus = new Label();

            this.pnlHeader.SuspendLayout();
            this.pnlCriteria.SuspendLayout();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvReport)).BeginInit();
            this.pnlSummary.SuspendLayout();
            this.pnlButtons.SuspendLayout();
            this.pnlFooter.SuspendLayout();
            this.SuspendLayout();

            // 
            // pnlHeader
            // 
            this.pnlHeader.BackColor = Color.FromArgb(46, 134, 171);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Location = new Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new Size(1200, 60);
            this.pnlHeader.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 18);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 27);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "التقارير والكشوفات";

            // 
            // pnlCriteria
            // 
            this.pnlCriteria.BackColor = Color.White;
            this.pnlCriteria.BorderStyle = BorderStyle.FixedSingle;
            this.pnlCriteria.Controls.Add(this.lblDateFrom);
            this.pnlCriteria.Controls.Add(this.dtpDateFrom);
            this.pnlCriteria.Controls.Add(this.lblDateTo);
            this.pnlCriteria.Controls.Add(this.dtpDateTo);
            this.pnlCriteria.Controls.Add(this.lblReportType);
            this.pnlCriteria.Controls.Add(this.cmbReportType);
            this.pnlCriteria.Controls.Add(this.lblReportDescription);
            this.pnlCriteria.Controls.Add(this.btnGenerateReport);
            this.pnlCriteria.Dock = DockStyle.Top;
            this.pnlCriteria.Location = new Point(0, 60);
            this.pnlCriteria.Name = "pnlCriteria";
            this.pnlCriteria.Padding = new Padding(20, 15, 20, 15);
            this.pnlCriteria.Size = new Size(1200, 120);
            this.pnlCriteria.TabIndex = 1;

            // 
            // lblDateFrom
            // 
            this.lblDateFrom.AutoSize = true;
            this.lblDateFrom.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblDateFrom.Location = new Point(25, 25);
            this.lblDateFrom.Name = "lblDateFrom";
            this.lblDateFrom.Size = new Size(70, 19);
            this.lblDateFrom.TabIndex = 0;
            this.lblDateFrom.Text = "من تاريخ:";

            // 
            // dtpDateFrom
            // 
            this.dtpDateFrom.Font = new Font("Tahoma", 12F);
            this.dtpDateFrom.Format = DateTimePickerFormat.Short;
            this.dtpDateFrom.Location = new Point(110, 22);
            this.dtpDateFrom.Name = "dtpDateFrom";
            this.dtpDateFrom.Size = new Size(150, 27);
            this.dtpDateFrom.TabIndex = 1;

            // 
            // lblDateTo
            // 
            this.lblDateTo.AutoSize = true;
            this.lblDateTo.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblDateTo.Location = new Point(290, 25);
            this.lblDateTo.Name = "lblDateTo";
            this.lblDateTo.Size = new Size(70, 19);
            this.lblDateTo.TabIndex = 2;
            this.lblDateTo.Text = "إلى تاريخ:";

            // 
            // dtpDateTo
            // 
            this.dtpDateTo.Font = new Font("Tahoma", 12F);
            this.dtpDateTo.Format = DateTimePickerFormat.Short;
            this.dtpDateTo.Location = new Point(375, 22);
            this.dtpDateTo.Name = "dtpDateTo";
            this.dtpDateTo.Size = new Size(150, 27);
            this.dtpDateTo.TabIndex = 3;

            // 
            // lblReportType
            // 
            this.lblReportType.AutoSize = true;
            this.lblReportType.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblReportType.Location = new Point(555, 25);
            this.lblReportType.Name = "lblReportType";
            this.lblReportType.Size = new Size(90, 19);
            this.lblReportType.TabIndex = 4;
            this.lblReportType.Text = "نوع التقرير:";

            // 
            // cmbReportType
            // 
            this.cmbReportType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbReportType.Font = new Font("Tahoma", 12F);
            this.cmbReportType.FormattingEnabled = true;
            this.cmbReportType.Location = new Point(660, 22);
            this.cmbReportType.Name = "cmbReportType";
            this.cmbReportType.Size = new Size(200, 27);
            this.cmbReportType.TabIndex = 5;
            this.cmbReportType.SelectedIndexChanged += new EventHandler(this.cmbReportType_SelectedIndexChanged);

            // 
            // lblReportDescription
            // 
            this.lblReportDescription.AutoSize = true;
            this.lblReportDescription.Font = new Font("Tahoma", 10F);
            this.lblReportDescription.ForeColor = Color.FromArgb(108, 117, 125);
            this.lblReportDescription.Location = new Point(25, 65);
            this.lblReportDescription.Name = "lblReportDescription";
            this.lblReportDescription.Size = new Size(300, 17);
            this.lblReportDescription.TabIndex = 6;
            this.lblReportDescription.Text = "يعرض جميع الرعية النشطين مع ديونهم في الفترة المحددة";

            // 
            // btnGenerateReport
            // 
            this.btnGenerateReport.Anchor = AnchorStyles.Right;
            this.btnGenerateReport.BackColor = Color.FromArgb(40, 167, 69);
            this.btnGenerateReport.FlatAppearance.BorderSize = 0;
            this.btnGenerateReport.FlatStyle = FlatStyle.Flat;
            this.btnGenerateReport.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnGenerateReport.ForeColor = Color.White;
            this.btnGenerateReport.Location = new Point(1050, 20);
            this.btnGenerateReport.Name = "btnGenerateReport";
            this.btnGenerateReport.Size = new Size(130, 30);
            this.btnGenerateReport.TabIndex = 7;
            this.btnGenerateReport.Text = "📊 توليد التقرير";
            this.btnGenerateReport.UseVisualStyleBackColor = false;
            this.btnGenerateReport.Click += new EventHandler(this.btnGenerateReport_Click);

            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.dgvReport);
            this.pnlMain.Controls.Add(this.pnlSummary);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Location = new Point(0, 180);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new Padding(10);
            this.pnlMain.Size = new Size(1200, 450);
            this.pnlMain.TabIndex = 2;

            // 
            // dgvReport
            // 
            this.dgvReport.AllowUserToAddRows = false;
            this.dgvReport.AllowUserToDeleteRows = false;
            this.dgvReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvReport.BackgroundColor = Color.White;
            this.dgvReport.BorderStyle = BorderStyle.Fixed3D;
            this.dgvReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvReport.Dock = DockStyle.Fill;
            this.dgvReport.GridColor = Color.FromArgb(224, 224, 224);
            this.dgvReport.Location = new Point(10, 10);
            this.dgvReport.MultiSelect = false;
            this.dgvReport.Name = "dgvReport";
            this.dgvReport.ReadOnly = true;
            this.dgvReport.RowHeadersWidth = 30;
            this.dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvReport.Size = new Size(980, 430);
            this.dgvReport.TabIndex = 0;

            // 
            // pnlSummary
            // 
            this.pnlSummary.BackColor = Color.White;
            this.pnlSummary.BorderStyle = BorderStyle.FixedSingle;
            this.pnlSummary.Controls.Add(this.lblSummaryTitle);
            this.pnlSummary.Controls.Add(this.lblTotalRaayah);
            this.pnlSummary.Controls.Add(this.lblTotalDebt);
            this.pnlSummary.Controls.Add(this.lblTotalReceived);
            this.pnlSummary.Controls.Add(this.lblNetAmount);
            this.pnlSummary.Controls.Add(this.lblDiscountAmount);
            this.pnlSummary.Controls.Add(this.lblNetAfterDiscount);
            this.pnlSummary.Dock = DockStyle.Right;
            this.pnlSummary.Location = new Point(990, 10);
            this.pnlSummary.Name = "pnlSummary";
            this.pnlSummary.Padding = new Padding(15);
            this.pnlSummary.Size = new Size(200, 430);
            this.pnlSummary.TabIndex = 1;

            // 
            // lblSummaryTitle
            // 
            this.lblSummaryTitle.AutoSize = true;
            this.lblSummaryTitle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.lblSummaryTitle.ForeColor = Color.FromArgb(46, 134, 171);
            this.lblSummaryTitle.Location = new Point(15, 15);
            this.lblSummaryTitle.Name = "lblSummaryTitle";
            this.lblSummaryTitle.Size = new Size(120, 23);
            this.lblSummaryTitle.TabIndex = 0;
            this.lblSummaryTitle.Text = "ملخص التقرير";

            // 
            // lblTotalRaayah
            // 
            this.lblTotalRaayah.AutoSize = true;
            this.lblTotalRaayah.Font = new Font("Tahoma", 11F);
            this.lblTotalRaayah.Location = new Point(15, 60);
            this.lblTotalRaayah.Name = "lblTotalRaayah";
            this.lblTotalRaayah.Size = new Size(90, 18);
            this.lblTotalRaayah.TabIndex = 1;
            this.lblTotalRaayah.Text = "عدد الرعية: 0";

            // 
            // lblTotalDebt
            // 
            this.lblTotalDebt.AutoSize = true;
            this.lblTotalDebt.Font = new Font("Tahoma", 11F);
            this.lblTotalDebt.ForeColor = Color.FromArgb(46, 134, 171);
            this.lblTotalDebt.Location = new Point(15, 90);
            this.lblTotalDebt.Name = "lblTotalDebt";
            this.lblTotalDebt.Size = new Size(120, 18);
            this.lblTotalDebt.TabIndex = 2;
            this.lblTotalDebt.Text = "إجمالي الديون: 0";

            // 
            // lblTotalReceived
            // 
            this.lblTotalReceived.AutoSize = true;
            this.lblTotalReceived.Font = new Font("Tahoma", 11F);
            this.lblTotalReceived.ForeColor = Color.FromArgb(108, 117, 125);
            this.lblTotalReceived.Location = new Point(15, 120);
            this.lblTotalReceived.Name = "lblTotalReceived";
            this.lblTotalReceived.Size = new Size(110, 18);
            this.lblTotalReceived.TabIndex = 3;
            this.lblTotalReceived.Text = "إجمالي الواصل: 0";

            // 
            // lblNetAmount
            // 
            this.lblNetAmount.AutoSize = true;
            this.lblNetAmount.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblNetAmount.ForeColor = Color.FromArgb(220, 53, 69);
            this.lblNetAmount.Location = new Point(15, 150);
            this.lblNetAmount.Name = "lblNetAmount";
            this.lblNetAmount.Size = new Size(80, 19);
            this.lblNetAmount.TabIndex = 4;
            this.lblNetAmount.Text = "الصافي: 0";

            // 
            // lblDiscountAmount
            // 
            this.lblDiscountAmount.AutoSize = true;
            this.lblDiscountAmount.Font = new Font("Tahoma", 11F);
            this.lblDiscountAmount.ForeColor = Color.FromArgb(241, 143, 1);
            this.lblDiscountAmount.Location = new Point(15, 180);
            this.lblDiscountAmount.Name = "lblDiscountAmount";
            this.lblDiscountAmount.Size = new Size(120, 18);
            this.lblDiscountAmount.TabIndex = 5;
            this.lblDiscountAmount.Text = "خصم الحوالة: 0";
            this.lblDiscountAmount.Visible = false;

            // 
            // lblNetAfterDiscount
            // 
            this.lblNetAfterDiscount.AutoSize = true;
            this.lblNetAfterDiscount.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblNetAfterDiscount.ForeColor = Color.FromArgb(40, 167, 69);
            this.lblNetAfterDiscount.Location = new Point(15, 210);
            this.lblNetAfterDiscount.Name = "lblNetAfterDiscount";
            this.lblNetAfterDiscount.Size = new Size(150, 19);
            this.lblNetAfterDiscount.TabIndex = 6;
            this.lblNetAfterDiscount.Text = "الصافي بعد الخصم: 0";
            this.lblNetAfterDiscount.Visible = false;

            // 
            // pnlButtons
            // 
            this.pnlButtons.BackColor = Color.FromArgb(248, 249, 250);
            this.pnlButtons.BorderStyle = BorderStyle.FixedSingle;
            this.pnlButtons.Controls.Add(this.btnExportPdf);
            this.pnlButtons.Controls.Add(this.btnExportExcel);
            this.pnlButtons.Controls.Add(this.btnPrint);
            this.pnlButtons.Controls.Add(this.btnPrintPreview);
            this.pnlButtons.Controls.Add(this.btnClose);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Location = new Point(0, 630);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Padding = new Padding(10);
            this.pnlButtons.Size = new Size(1200, 60);
            this.pnlButtons.TabIndex = 3;

            // 
            // btnExportPdf
            // 
            this.btnExportPdf.BackColor = Color.FromArgb(220, 53, 69);
            this.btnExportPdf.Enabled = false;
            this.btnExportPdf.FlatAppearance.BorderSize = 0;
            this.btnExportPdf.FlatStyle = FlatStyle.Flat;
            this.btnExportPdf.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnExportPdf.ForeColor = Color.White;
            this.btnExportPdf.Location = new Point(15, 15);
            this.btnExportPdf.Name = "btnExportPdf";
            this.btnExportPdf.Size = new Size(120, 30);
            this.btnExportPdf.TabIndex = 0;
            this.btnExportPdf.Text = "📄 تصدير PDF";
            this.btnExportPdf.UseVisualStyleBackColor = false;
            this.btnExportPdf.Click += new EventHandler(this.btnExportPdf_Click);

            // 
            // btnExportExcel
            // 
            this.btnExportExcel.BackColor = Color.FromArgb(40, 167, 69);
            this.btnExportExcel.Enabled = false;
            this.btnExportExcel.FlatAppearance.BorderSize = 0;
            this.btnExportExcel.FlatStyle = FlatStyle.Flat;
            this.btnExportExcel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnExportExcel.ForeColor = Color.White;
            this.btnExportExcel.Location = new Point(145, 15);
            this.btnExportExcel.Name = "btnExportExcel";
            this.btnExportExcel.Size = new Size(130, 30);
            this.btnExportExcel.TabIndex = 1;
            this.btnExportExcel.Text = "📋 تصدير Excel";
            this.btnExportExcel.UseVisualStyleBackColor = false;
            this.btnExportExcel.Click += new EventHandler(this.btnExportExcel_Click);

            //
            // btnPrint
            //
            this.btnPrint.BackColor = Color.FromArgb(23, 162, 184);
            this.btnPrint.Enabled = false;
            this.btnPrint.FlatAppearance.BorderSize = 0;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.Location = new Point(285, 15);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new Size(100, 30);
            this.btnPrint.TabIndex = 2;
            this.btnPrint.Text = "🖨️ طباعة";
            this.btnPrint.UseVisualStyleBackColor = false;
            this.btnPrint.Click += new EventHandler(this.btnPrint_Click);

            //
            // btnPrintPreview
            //
            this.btnPrintPreview.BackColor = Color.FromArgb(108, 117, 125);
            this.btnPrintPreview.Enabled = false;
            this.btnPrintPreview.FlatAppearance.BorderSize = 0;
            this.btnPrintPreview.FlatStyle = FlatStyle.Flat;
            this.btnPrintPreview.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnPrintPreview.ForeColor = Color.White;
            this.btnPrintPreview.Location = new Point(175, 15);
            this.btnPrintPreview.Name = "btnPrintPreview";
            this.btnPrintPreview.Size = new Size(100, 30);
            this.btnPrintPreview.TabIndex = 3;
            this.btnPrintPreview.Text = "👁️ معاينة";
            this.btnPrintPreview.UseVisualStyleBackColor = false;
            this.btnPrintPreview.Click += new EventHandler(this.btnPrintPreview_Click);

            //
            // btnClose
            // 
            this.btnClose.Anchor = AnchorStyles.Right;
            this.btnClose.BackColor = Color.FromArgb(108, 117, 125);
            this.btnClose.FlatAppearance.BorderSize = 0;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnClose.ForeColor = Color.White;
            this.btnClose.Location = new Point(1075, 15);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.TabIndex = 3;
            this.btnClose.Text = "❌ إغلاق";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            // 
            // pnlFooter
            // 
            this.pnlFooter.BackColor = Color.FromArgb(248, 249, 250);
            this.pnlFooter.BorderStyle = BorderStyle.FixedSingle;
            this.pnlFooter.Controls.Add(this.lblStatus);
            this.pnlFooter.Dock = DockStyle.Bottom;
            this.pnlFooter.Location = new Point(0, 690);
            this.pnlFooter.Name = "pnlFooter";
            this.pnlFooter.Size = new Size(1200, 30);
            this.pnlFooter.TabIndex = 4;

            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Tahoma", 10F);
            this.lblStatus.Location = new Point(10, 6);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(100, 17);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "جاهز لتوليد التقارير";

            // 
            // ReportsForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 720);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlButtons);
            this.Controls.Add(this.pnlCriteria);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlFooter);
            this.Font = new Font("Tahoma", 12F);
            this.MinimumSize = new Size(1000, 600);
            this.Name = "ReportsForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "التقارير والكشوفات - شركة الغشمي";

            this.pnlHeader.ResumeLayout(false);
            this.pnlHeader.PerformLayout();
            this.pnlCriteria.ResumeLayout(false);
            this.pnlCriteria.PerformLayout();
            this.pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvReport)).EndInit();
            this.pnlSummary.ResumeLayout(false);
            this.pnlSummary.PerformLayout();
            this.pnlButtons.ResumeLayout(false);
            this.pnlFooter.ResumeLayout(false);
            this.pnlFooter.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private Panel pnlCriteria;
        private Label lblDateFrom;
        private DateTimePicker dtpDateFrom;
        private Label lblDateTo;
        private DateTimePicker dtpDateTo;
        private Label lblReportType;
        private ComboBox cmbReportType;
        private Label lblReportDescription;
        private Button btnGenerateReport;
        private Panel pnlMain;
        private DataGridView dgvReport;
        private Panel pnlSummary;
        private Label lblSummaryTitle;
        private Label lblTotalRaayah;
        private Label lblTotalDebt;
        private Label lblTotalReceived;
        private Label lblNetAmount;
        private Label lblDiscountAmount;
        private Label lblNetAfterDiscount;
        private Panel pnlButtons;
        private Button btnExportPdf;
        private Button btnExportExcel;
        private Button btnPrint;
        private Button btnPrintPreview;
        private Button btnClose;
        private Panel pnlFooter;
        private Label lblStatus;
    }
}
