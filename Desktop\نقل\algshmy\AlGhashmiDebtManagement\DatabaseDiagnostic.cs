using AlGhashmiDebtManagement.Data;
using Microsoft.EntityFrameworkCore;

namespace AlGhashmiDebtManagement
{
    /// <summary>
    /// أداة تشخيص قاعدة البيانات
    /// </summary>
    public static class DatabaseDiagnostic
    {
        /// <summary>
        /// فحص حالة قاعدة البيانات
        /// </summary>
        public static void DiagnoseDatabase()
        {
            Console.WriteLine("=== تشخيص قاعدة البيانات ===");
            Console.WriteLine();

            try
            {
                using var context = new AppDbContext();
                
                // فحص مسار قاعدة البيانات
                var connectionString = context.Database.GetConnectionString();
                Console.WriteLine($"نص الاتصال: {connectionString}");
                
                var dbPath = ExtractDbPath(connectionString);
                Console.WriteLine($"مسار قاعدة البيانات: {dbPath}");
                Console.WriteLine($"هل الملف موجود: {(File.Exists(dbPath) ? "نعم" : "لا")}");
                
                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    Console.WriteLine($"حجم الملف: {fileInfo.Length} بايت");
                    Console.WriteLine($"تاريخ الإنشاء: {fileInfo.CreationTime}");
                    Console.WriteLine($"تاريخ آخر تعديل: {fileInfo.LastWriteTime}");
                }
                
                Console.WriteLine();
                
                // فحص إمكانية الاتصال
                Console.WriteLine("=== فحص الاتصال ===");
                var canConnect = context.Database.CanConnect();
                Console.WriteLine($"إمكانية الاتصال: {(canConnect ? "نجح" : "فشل")}");
                
                if (canConnect)
                {
                    // فحص الجداول
                    Console.WriteLine();
                    Console.WriteLine("=== فحص الجداول ===");
                    
                    try
                    {
                        var raayahCount = context.Raayah.Count();
                        Console.WriteLine($"عدد الرعية: {raayahCount}");
                        
                        var weeklyDebtsCount = context.WeeklyDebts.Count();
                        Console.WriteLine($"عدد الديون الأسبوعية: {weeklyDebtsCount}");
                        
                        // عرض أول 3 رعية
                        if (raayahCount > 0)
                        {
                            Console.WriteLine();
                            Console.WriteLine("=== عينة من الرعية ===");
                            var sampleRaayah = context.Raayah.Take(3).ToList();
                            foreach (var raayah in sampleRaayah)
                            {
                                Console.WriteLine($"- {raayah.FullName} (نشط: {raayah.IsActive})");
                            }
                        }
                        
                        // عرض أول 3 ديون
                        if (weeklyDebtsCount > 0)
                        {
                            Console.WriteLine();
                            Console.WriteLine("=== عينة من الديون الأسبوعية ===");
                            var sampleDebts = context.WeeklyDebts
                                .Include(d => d.Raayah)
                                .Take(3)
                                .ToList();
                            foreach (var debt in sampleDebts)
                            {
                                Console.WriteLine($"- {debt.Raayah?.FullName}: {debt.DateFrom:dd/MM/yyyy} - {debt.DateTo:dd/MM/yyyy}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في قراءة البيانات: {ex.Message}");
                    }
                }
                
                // فحص Migrations
                Console.WriteLine();
                Console.WriteLine("=== فحص Migrations ===");
                try
                {
                    var appliedMigrations = context.Database.GetAppliedMigrations().ToList();
                    Console.WriteLine($"عدد Migrations المطبقة: {appliedMigrations.Count}");
                    foreach (var migration in appliedMigrations)
                    {
                        Console.WriteLine($"- {migration}");
                    }
                    
                    var pendingMigrations = context.Database.GetPendingMigrations().ToList();
                    Console.WriteLine($"عدد Migrations المعلقة: {pendingMigrations.Count}");
                    foreach (var migration in pendingMigrations)
                    {
                        Console.WriteLine($"- {migration}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في فحص Migrations: {ex.Message}");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ عام في التشخيص: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("=== انتهى التشخيص ===");
        }
        
        /// <summary>
        /// استخراج مسار قاعدة البيانات من نص الاتصال
        /// </summary>
        private static string ExtractDbPath(string? connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return "غير محدد";
                
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split('=');
                if (keyValue.Length == 2 && keyValue[0].Trim().ToLower() == "data source")
                {
                    return keyValue[1].Trim();
                }
            }
            
            return "غير موجود";
        }
        
        /// <summary>
        /// إعادة إنشاء قاعدة البيانات
        /// </summary>
        public static void RecreateDatabase()
        {
            Console.WriteLine("=== إعادة إنشاء قاعدة البيانات ===");

            try
            {
                // حذف ملف قاعدة البيانات مباشرة
                var dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "AlGhashmiDebtManagement",
                    "AlGhashmiDebtManagement.db"
                );

                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                    Console.WriteLine($"تم حذف ملف قاعدة البيانات: {dbPath}");
                }

                // حذف ملفات إضافية إن وجدت
                var dbDir = Path.GetDirectoryName(dbPath);
                if (Directory.Exists(dbDir))
                {
                    var relatedFiles = Directory.GetFiles(dbDir, "*.db*");
                    foreach (var file in relatedFiles)
                    {
                        try
                        {
                            File.Delete(file);
                            Console.WriteLine($"تم حذف ملف: {file}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"تعذر حذف ملف {file}: {ex.Message}");
                        }
                    }
                }

                using var context = new AppDbContext();

                // إنشاء قاعدة البيانات الجديدة
                if (context.Database.EnsureCreated())
                {
                    Console.WriteLine("تم إنشاء قاعدة البيانات الجديدة");
                }

                // إضافة البيانات الأولية
                Data.DatabaseInitializer.Initialize(context);
                Console.WriteLine("تم إضافة البيانات الأولية");

                Console.WriteLine("تمت إعادة إنشاء قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إعادة إنشاء قاعدة البيانات: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex}");
            }
        }

        /// <summary>
        /// حذف قاعدة البيانات نهائياً
        /// </summary>
        public static void DeleteDatabase()
        {
            Console.WriteLine("=== حذف قاعدة البيانات ===");

            try
            {
                var dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "AlGhashmiDebtManagement",
                    "AlGhashmiDebtManagement.db"
                );

                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                    Console.WriteLine($"تم حذف ملف قاعدة البيانات: {dbPath}");
                }
                else
                {
                    Console.WriteLine("ملف قاعدة البيانات غير موجود");
                }

                // حذف المجلد إذا كان فارغاً
                var dbDir = Path.GetDirectoryName(dbPath);
                if (Directory.Exists(dbDir) && !Directory.GetFiles(dbDir).Any())
                {
                    Directory.Delete(dbDir);
                    Console.WriteLine($"تم حذف مجلد قاعدة البيانات: {dbDir}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حذف قاعدة البيانات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار العمليات الأساسية
        /// </summary>
        public static void TestBasicOperations()
        {
            Console.WriteLine("=== اختبار العمليات الأساسية ===");
            
            try
            {
                using var context = new AppDbContext();
                
                // اختبار إضافة رعوي جديد
                var testRaayah = new Models.Raayah
                {
                    FullName = "اختبار قاعدة البيانات",
                    EnableDiscount = false,
                    InKashfOzri = false,
                    InKharijKashf = false,
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    Notes = "رعوي اختبار"
                };
                
                context.Raayah.Add(testRaayah);
                var result = context.SaveChanges();
                Console.WriteLine($"تم إضافة رعوي اختبار - السجلات المتأثرة: {result}");
                
                // اختبار قراءة البيانات
                var count = context.Raayah.Count();
                Console.WriteLine($"إجمالي عدد الرعية: {count}");
                
                // اختبار حذف الرعوي الاختبار
                context.Raayah.Remove(testRaayah);
                result = context.SaveChanges();
                Console.WriteLine($"تم حذف رعوي الاختبار - السجلات المتأثرة: {result}");
                
                Console.WriteLine("نجحت جميع العمليات الأساسية");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار العمليات الأساسية: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex}");
            }
        }
    }
}
